# Read Agent

## 简介

Read Agent 是一个智能阅读助手，旨在将您的电子书（目前支持 `.epub` 格式）转化为一个可交互的知识库。本项目是一个基于检索增强生成（RAG）技术的命令行应用，它能深入理解书籍内容，并用自然语言回答您关于书中的任何问题。

您可以将书籍放入 `corpus` 文件夹，程序会自动处理它们，将其内容分解、向量化并存储。之后，您就可以通过命令行与它进行对话了。

## 核心功能

- **自动化书籍处理**: 自动检测 `corpus` 目录下的新书，并启动完整的处理流水线。
- **深度内容解析**:
    - 解析 EPUB 文件，提取元数据和章节。
    - 将文本智能切分为段落、句子，并进一步组织成逻辑“页面”。
    - 自动检测书籍内容的主要语言（支持中英文）。
- **多粒度向量检索**:
    - 使用先进的嵌入模型（如 `bge-large-zh-v1.5`）为句子、页面和AI生成的摘要创建向量索引。
    - 在回答问题时，同时在句子、页面、摘要三个层级进行向量检索，确保找到最相关的信息。
    - 采用倒数排序融合（RRF）算法合并多层检索结果，提高上下文的准确性。
- **大语言模型（LLM）集成**:
    - 支持多种 LLM 后端，包括 Ollama、Deepseek、Kimi 等，易于扩展。
    - 利用 LLM 基于检索到的上下文，生成流畅、准确的回答。
- **高亮参考来源**: 在给出答案的同时，明确展示答案所依据的书中最相关的原文片段，方便溯源。
- **可扩展的插件系统**: 基于 `pluggy` 构建，允许开发者轻松添加新的提示词模板或自定义页面处理逻辑。
- **增量处理与断点续传**: 能够识别已处理过的书籍和数据，只对新增内容进行处理，节省时间和计算资源。

## 项目架构

Read Agent 采用模块化的架构，各组件职责分明：

1.  **主程序 (`main.py`)**: 应用入口，负责解析命令行参数、启动程序、管理主流程和交互式查询循环。
2.  **书籍解析器 (`book_parsers/`)**: 负责解析 `.epub` 文件，提取文本内容和元数据。
3.  **存储管理器 (`storage/`)**: 核心的数据抽象层，封装了与 SQLite 和 ChromaDB 的所有交互。
    - **SQLite**: 用于存储书籍元数据、章节、句子、页面等结构化信息。
    - **ChromaDB**: 作为向量数据库，存储所有文本（页面、摘要、句子）的向量嵌入。
4.  **处理流水线 (`processing/`)**:
    - `core_processor.py`: 定义了书籍处理的核心逻辑，包括调用解析器、分页、调用插件处理等。
    - `text_utils.py`: 提供文本处理的工具函数，如语言检测、智能分句等。
5.  **LLM 模块 (`llm/`)**:
    - `llm_interface.py`: 封装了对不同大语言模型 API 的调用。
    - `embedding.py`: 负责调用嵌入模型服务，为文本生成向量。
    - `prompt_manager.py`: 管理和加载所有提示词模板。
6.  **查询管理器 (`query/`)**: 实现了 RAG 的核心逻辑。接收用户问题，执行多粒度检索，构建提示，调用 LLM，并返回最终答案。
7.  **插件系统 (`hooks/`, `system_plugins/`, `user_plugins/`)**:
    - `hooks/`: 定义插件接口规范 (`hookspecs`)。
    - `system_plugins/`: 包含系统默认的插件，如默认的提示词和页面摘要生成器。
    - `user_plugins/`: 用户可以自行添加插件以扩展系统功能。
8.  **导航模块 (`navigation/`)**: 提供了更高级的文本定位功能，可以根据文本内容精确查找其在书中的位置，为未来构建图形化界面或更精细的引用功能打下基础。

## 如何开始

### 1. 环境准备

- Python 3.9+
- 推荐设置一个虚拟环境：
  ```bash
  python -m venv .venv
  source .venv/bin/activate
  ```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置

- **LLM API Keys**: 如果您使用 Deepseek 或 Kimi 等商业模型，请在 `config.py` 文件中或通过环境变量设置您的 API Key。
  ```python
  # 在 config.py 中
  LLM_CONFIG = {
      "providers": {
          "deepseek": {
              "api_key": "sk-your-deepseek-api-key",
              # ...
          },
          "kimi": {
              "api_key": "sk-your-kimi-api-key",
              # ...
          }
      }
  }
  ```
- **模型选择**: 在 `config.py` 中，您可以选择默认的 LLM 提供商和嵌入模型。本项目默认使用 Ollama，请确保您已在本地运行 Ollama 并下载了所需的模型。

### 4. 运行 Ollama (如果使用)

请确保 Ollama 服务正在运行，并已拉取所需的模型。
```bash
# 拉取聊天模型
ollama pull qwen3:8b

# 拉取嵌入模型
ollama pull quentinz/bge-large-zh-v1.5:latest
```

## 如何使用

### 1. 添加书籍

将您的 `.epub` 格式的电子书文件复制到项目根目录下的 `corpus` 文件夹中。

### 2. 启动程序并处理书籍

首次运行时，程序会自动检测并处理 `corpus` 文件夹中的所有书籍。

```bash
python main.py
```

程序会显示详细的处理日志。处理完成后，会提示您可以开始提问。

**强制重新处理**: 如果您想清空所有数据并从头开始处理，可以使用 `--rebuild` 参数。

```bash
python main.py --rebuild
```

### 3. 开始提问

处理完成后，您可以在命令行中输入问题，然后按回车。

```
> 请输入你的问题: 枪炮、病菌与钢铁这本书主要探讨了什么核心问题？
```

程序会返回由 LLM 生成的答案，并在下方附上相关的原文参考。

输入 `exit` 或 `quit` 来退出程序。

### 4. 删除书籍

项目提供了一个独立的脚本来帮助您从数据库中删除某本书籍及其所有相关数据。

```bash
# 首先，列出所有已存入数据库的书籍及其ID
python storage/remove_book.py --list

# 然后，使用获取到的ID来删除书籍
python storage/remove_book.py --id <book_id>
```

## 目录结构

```
/
├── book_parsers/         # EPUB文件解析器
├── corpus/               # 存放你的电子书
├── data/                 # 存储数据库和日志
│   ├── books.db          # SQLite 数据库
│   └── chroma_db/        # ChromaDB 向量数据库
├── docs/                 # 项目文档
├── hooks/                # Pluggy插件接口定义
├── llm/                  # 大语言模型交互模块
├── navigation/           # 文本定位与导航模块
├── processing/           # 核心处理流水线
├── query/                # RAG查询管理模块
├── storage/              # 数据库和向量存储管理
├── system_plugins/       # 系统默认插件
├── user_plugins/         # 用户自定义插件
├── config.py             # 全局配置文件
├── main.py               # 程序主入口
├── requirements.txt      # Python依赖
└── README.md             # 本文档
```

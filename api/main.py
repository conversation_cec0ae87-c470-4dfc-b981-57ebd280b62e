from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from typing import List, Dict, Any
import os
from fastapi.staticfiles import StaticFiles
from api.models import Book, ContentBlock, Sentence
from storage.storage_manager import StorageManager # Import the class
import json # Import json module
from fastapi.middleware.cors import CORSMiddleware # Import CORSMiddleware

app = FastAPI()

# Mount the media directory to serve static files
app.mount("/media", StaticFiles(directory="media"), name="media")

# Instantiate StorageManager
sm = StorageManager()

# Add CORS middleware
origins = [
    "http://localhost",
    "http://localhost:5173", # Frontend origin
    "http://127.0.0.1",
    "http://127.0.0.1:5173",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def read_root():
    return {"message": "Hello World from FastAPI!"}

@app.get("/books", response_model=List[Dict[str, Any]])
async def get_all_books():
    """
    Retrieves a list of all books from the database.
    """
    all_books = sm.get_all_books()
    if not all_books:
        return []
    # Clean up the source path to just return the filename
    for book in all_books:
        if book.get('source_path'):
            book['source_path'] = os.path.basename(book['source_path'])
    return all_books


# Removed the /books endpoint

@app.get("/book/{book_id}", response_model=Book)
async def get_book_content(book_id: str):
    book_details_raw = sm.get_book_by_id(book_id)
    if not book_details_raw:
        raise HTTPException(status_code=404, detail="Book not found")

    # Pass the raw metadata dictionary
    metadata_dict = json.loads(book_details_raw['metadata'])
    
    # Fetch content blocks for the book
    content_blocks_data = sm.get_content_blocks_for_book(book_id)
    
    content_blocks = []
    for block_data in content_blocks_data:
        sentences_data = sm.get_sentences_for_block(block_data['id'])
        sentences = [
            Sentence(
                sentence_id=s['id'],
                page_id=s['page_id'] if 'page_id' in s else 'unknown_page',
                text=s['content']
            ) for s in sentences_data
        ]
        
        # Parse metadata if it exists
        block_metadata = None
        if block_data.get('metadata'):
            try:
                block_metadata = json.loads(block_data['metadata'])
            except json.JSONDecodeError:
                block_metadata = None # Or handle error appropriately

        content_blocks.append(
            ContentBlock(
                block_id=block_data['id'],
                block_type=block_data.get('block_type'),
                sentences=sentences,
                metadata=block_metadata
            )
        )
    
    return Book(
        book_id=book_details_raw['id'],
        metadata=metadata_dict, # Pass the entire metadata dictionary
        content_blocks=content_blocks
    )

from pydantic import BaseModel
from typing import List, Optional, Dict, Any

class Sentence(BaseModel):
    sentence_id: str
    page_id: str
    text: str

class ContentBlock(BaseModel):
    block_id: str
    block_type: Optional[str] = None
    sentences: List[Sentence]
    metadata: Optional[Dict[str, Any]] = None

class Book(BaseModel):
    book_id: str
    metadata: Dict[str, Any] # Changed from title and author
    content_blocks: List[ContentBlock]

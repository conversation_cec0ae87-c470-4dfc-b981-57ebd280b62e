# dev/config.py
import os

# --- 路径配置 ---
# 获取此配置文件所在目录的绝对路径，我们将其作为项目根目录。
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
# 将DATA_DIR定义为绝对路径，以避免相对路径带来的混淆。
DATA_DIR = os.path.join(PROJECT_ROOT, "data")

# 默认处理配置，区分中英文
DEFAULT_CONFIG = {
    'zh': {
        'unit_limit': 1000,         # 分页时每页的单位上限（混合单位）
        'start_threshold': 300,    # 开始寻找断点标记的单位阈值
        'max_lookup_pages': 5,      # 查找相关页面时返回的最大页面数
        'temperature': 0.0,         # LLM 生成时的温度参数，0.0 表示更确定的输出
        'unit_type': 'char_mixed'   # 中文的单位计数类型：混合模式
    },
    'en': {
        'unit_limit': 1000,         # 分页时每页的单位上限（单词数）
        'start_threshold': 300,    # 开始寻找断点标记的单位阈值
        'max_lookup_pages': 5,      # 查找相关页面时返回的最大页面数
        'temperature': 0.0,         # LLM 生成时的温度参数
        'unit_type': 'word'         # 英文的单位计数类型：单词
    }
}

# 处理配置
PROCESSING_CONFIG = {
    'max_workers': 1  # 处理页面时的最大并发工作线程数
}

# LLM API 配置
LLM_CONFIG = {
    "default_provider": "lmstudio",
    "embedding_provider": "lmstudio_embedding",
    "providers": {
        "lmstudio_embedding": {
            "api_url": "http://127.0.0.1:1234/v1/embeddings",
            "model": "text-embedding-bge-large-zh-v1.5",
            "max_retries": 3
        },
        "ollama_embedding": {
            "api_url": "http://localhost:11434/api/embeddings",
            "model": "quentinz/bge-large-zh-v1.5:latest",
            "max_retries": 3
        },
        "ollama": {
            "api_url": "http://localhost:11434/api/chat",
            "model": "qwen3:8b",
            "max_tokens": 4096,
            "max_retries": 5
        },
        "deepseek": {
            "api_url": "https://api.deepseek.com/chat/completions",
            "api_key": os.getenv("DEEPSEEK_API_KEY", "***********************************"),
            "model": "deepseek-chat",
            "max_tokens": 4096,
            "max_retries": 3
        },
        "kimi": {
            "api_url": "https://api.moonshot.cn/v1/chat/completions",
            "api_key": os.getenv("KIMI_API_KEY", "sk-YdaW23x7OIF3XcKjwIoTWoXpPh4E7Z0dp3wNK2e1FIqTwy6q"),
            "model": "kimi-k2-0711-preview",
            "max_tokens": 4096,
            "max_retries": 3
        },
        "lmstudio": {
            "api_url": "http://127.0.0.1:1234/v1/chat/completions",
            "api_key": "not-required",
            "model": "qwen/qwen3-30b-a3b-2507",
            "max_tokens": 4096,
            "max_retries": 3
        }
    }
}

# 数据库配置 (DATA_DIR 现在是绝对路径)
SQLITE_DB_NAME = "books.db"
CHROMA_COLLECTION_NAME = "read_agent_pages_v2"
CHROMA_SUMMARY_COLLECTION_NAME = "read_agent_summaries_v2"
CHROMA_SENTENCE_COLLECTION_NAME = "read_agent_sentences_v2"

# 调试配置
DEBUG_MODE = False

# 语言代码到提示中语言名称的映射
LANGUAGE_MAPPING = {
    'zh': 'Chinese (中文)',
    'en': 'English'
}

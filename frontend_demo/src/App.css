:root {
  --background-color: #fdfaf4;
  --text-color: #333333;
  --highlight-color: #fdeec9;
  --header-color: #5c4b3c;
}

body {
  background-color: var(--background-color);
  color: var(--text-color);
  font-family: 'Georgia', 'Times New Roman', serif;
  margin: 0;
}

#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: left;
}

/* General App layout */
.App {
  text-align: left;
}

/* Book reader container */
.book-reader {
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.8;
}

.book-reader h1 {
  font-size: 2.8rem;
  margin-bottom: 0.5rem;
  color: var(--header-color);
  font-family: sans-serif; /* A slightly more modern font for headers */
}

.book-reader h2 {
  font-size: 1.5rem;
  font-style: italic;
  color: #777;
  margin-top: 0;
  margin-bottom: 3rem;
  font-family: sans-serif;
}

/* Paragraphs and other block types */
.book-reader p {
  margin-bottom: 1.2rem;
}

/* Interactive sentence styling */
.interactive-sentence {
  cursor: pointer;
  transition: background-color 0.3s;
  border-radius: 3px;
}

.interactive-sentence:hover {
  background-color: var(--highlight-color);
}
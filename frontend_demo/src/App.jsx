import React, { useState, useCallback } from 'react';
import bookData from './mock-data.json';
import './App.css';
import InteractionPopup from './InteractionPopup';

// Sentence Component
const Sentence = ({ sentence }) => {
  return (
    <span
      data-sentence-id={sentence.sentence_id}
      data-page-id={sentence.page_id}
      className="interactive-sentence"
    >
      {sentence.text}{' '}
    </span>
  );
};

// ContentBlock Component
const ContentBlock = ({ block }) => {
  const Tag = block.block_type || 'p';
  return (
    <Tag data-block-id={block.block_id}>
      {block.sentences.map(sentence => (
        <Sentence key={sentence.sentence_id} sentence={sentence} />
      ))}
    </Tag>
  );
};

// BookReader Component
const BookReader = ({ book, onTextSelect }) => {
  return (
    <article 
      className="book-reader"
      onMouseUp={onTextSelect}
    >
      <h1>{book.title}</h1>
      <h2>{book.author}</h2>
      {book.content_blocks.map(block => (
        <ContentBlock key={block.block_id} block={block} />
      ))}
    </article>
  );
};

// Main App Component
function App() {
  const [popupData, setPopupData] = useState(null);

  const handleTextSelection = useCallback(() => {
    const selection = window.getSelection();
    const selectedText = selection.toString().trim();

    if (selectedText) {
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      
      setPopupData({
        text: selectedText,
        position: { x: rect.left, y: rect.bottom + window.scrollY },
      });
    } else {
      // This allows clicks to close the popover
      setPopupData(null);
    }
  }, []);

  const handleClosePopup = () => {
    setPopupData(null);
  };

  return (
    <div className="App">
      <BookReader book={bookData} onTextSelect={handleTextSelection} />
      {popupData && (
        <InteractionPopup
          text={popupData.text}
          position={popupData.position}
          onClose={handleClosePopup}
        />
      )}
    </div>
  );
}

export default App;
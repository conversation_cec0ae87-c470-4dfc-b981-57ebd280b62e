.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2); /* Slightly darker overlay */
  z-index: 1000;
}

.popup-content {
  position: absolute;
  width: 350px;
  background: #ffffff;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
  padding: 1rem;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  gap: 1rem; /* Increased gap */
  font-family: sans-serif;
}

.popup-content p {
    margin: 0;
    font-size: 0.9rem;
    color: #555;
}

.selected-text {
  margin: 0;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-left: 4px solid #646cff;
  font-style: italic;
  font-size: 0.95rem;
  max-height: 150px;
  overflow-y: auto;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag-button {
  background-color: #e9ecef;
  color: #343a40;
  border: 1px solid #dee2e6;
  padding: 6px 10px;
  border-radius: 15px; /* Pill-shaped */
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
  font-size: 0.85rem;
}

.tag-button:hover {
  background-color: #dee2e6;
  border-color: #adb5bd;
}

.popup-textarea {
  width: 100%;
  min-height: 60px;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 0.5rem;
  font-family: inherit;
  resize: vertical;
}

.ai-response {
  padding: 0.75rem;
  background: #eef2ff; /* Light blue background for AI response */
  border-radius: 4px;
}

.popup-button {
  align-self: flex-end;
  background-color: #6c757d; /* Muted close button */
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.popup-button:hover {
    background-color: #5a6268;
}
import React, { useState } from 'react';
import './InteractionPopup.css';

const InteractionPopup = ({ text, position, onClose }) => {
  const [aiResponse, setAiResponse] = useState('');
  const tags = ['Summarize', 'Explain', 'Translate'];

  const handleTagClick = (tag) => {
    // Simulate an AI response based on the tag
    switch (tag) {
      case 'Summarize':
        setAiResponse('This is a simulated summary of the selected text. The old man, Santiago, has had a long streak of bad luck but remains hopeful.');
        break;
      case 'Explain':
        setAiResponse('This is a simulated explanation. "Salao" is a Cuban Spanish term for the worst kind of bad luck.');
        break;
      case 'Translate':
        setAiResponse('This is a simulated translation to a different language.');
        break;
      default:
        setAiResponse('Response for the selected tag.');
    }
  };

  if (!text) return null;

  const style = {
    left: `${position.x}px`,
    top: `${position.y}px`,
  };

  return (
    <div className="popup-overlay" onClick={onClose}>
      <div className="popup-content" style={style} onClick={(e) => e.stopPropagation()}>
        <p><strong>You selected:</strong></p>
        <blockquote className="selected-text">
          {text}
        </blockquote>
        
        {aiResponse ? (
          <div className="ai-response">
            <p><strong>AI Response:</strong></p>
            <p>{aiResponse}</p>
          </div>
        ) : (
          <>
            <div className="tag-container">
              {tags.map(tag => (
                <button key={tag} className="tag-button" onClick={() => handleTagClick(tag)}>
                  {tag}
                </button>
              ))}
            </div>
            <textarea
              className="popup-textarea"
              placeholder="Or ask a custom question..."
            />
          </>
        )}

        <button className="popup-button" onClick={onClose}>Close</button>
      </div>
    </div>
  );
};

export default InteractionPopup;
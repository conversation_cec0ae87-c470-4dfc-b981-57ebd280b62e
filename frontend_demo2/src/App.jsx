import React, { useState, useEffect, useCallback } from 'react';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import Typography from '@mui/material/Typography';
import BookReader from './BookReader';
import BookSelector from './BookSelector';
import InteractionPopup from './InteractionPopup';

function App() {
  const [books, setBooks] = useState([]);
  const [bookId, setBookId] = useState(null);
  const [bookContent, setBookContent] = useState(null);
  const [loading, setLoading] = useState(true); // Start with loading true for initial book list fetch
  const [error, setError] = useState(null);
  const [selection, setSelection] = useState({ text: null, position: null });

  // Fetch the list of books on component mount
  useEffect(() => {
    const fetchBooks = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch('http://127.0.0.1:8000/books');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setBooks(data);
      } catch (e) {
        setError(e.message);
      } finally {
        setLoading(false);
      }
    };
    fetchBooks();
  }, []);

  // Fetch book content when bookId changes
  useEffect(() => {
    const fetchBookContent = async () => {
      if (!bookId) {
        setBookContent(null);
        return;
      }

      setLoading(true);
      setError(null);
      try {
        const response = await fetch(`http://127.0.0.1:8000/book/${bookId}`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setBookContent(data);
      } catch (e) {
        setError(e.message);
      } finally {
        setLoading(false);
      }
    };

    fetchBookContent();
  }, [bookId]);

  const handleTextSelection = useCallback(() => {
    const currentSelection = window.getSelection();
    const selectedText = currentSelection.toString().trim();

    if (selectedText) {
      const range = currentSelection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      
      setSelection({
        text: selectedText,
        position: {
          x: rect.left + window.scrollX,
          y: rect.top + window.scrollY,
          width: rect.width,
          height: rect.height,
        },
      });
    } else {
      setSelection({ text: null, position: null });
    }
  }, []);

  const handleClosePopup = () => {
    setSelection({ text: null, position: null });
  };

  const handleSelectBook = (id) => {
    setBookId(id);
  };
  
  const handleCloseBook = () => {
    setBookId(null);
  };

  // Render loading indicator for the initial book list fetch
  if (loading && books.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error message if book list fails to load
  if (error && books.length === 0) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
            <Alert severity="error">Error loading library: {error}</Alert>
        </Box>
      );
  }

  // Main render logic: show reader or library
  return (
    <Box sx={{ fontFamily: 'Georgia, serif', height: '100vh', width: '100vw', overflow: 'hidden' }}>
      {loading && <CircularProgress sx={{ position: 'absolute', top: '50%', left: '50%' }}/>}
      {!loading && bookContent && bookId ? (
        // READER VIEW
        <>
          <BookReader 
            book={bookContent} 
            onTextSelect={handleTextSelection}
            onCloseBook={handleCloseBook}
          />
          <InteractionPopup
            selection={selection}
            isOpen={!!selection.text}
            onClose={handleClosePopup}
          />
        </>
      ) : (
        // LIBRARY VIEW
        <Box sx={{ p: 4, bgcolor: 'background.paper', height: '100vh' }}>
          <Typography variant="h2" component="h1" gutterBottom textAlign="center">
            My Library
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center' }}>
            <BookSelector 
              books={books} 
              onSelectBook={handleSelectBook} 
              selectedBookId={bookId} 
            />
          </Box>
           {error && <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>}
        </Box>
      )}
    </Box>
  );
}

export default App;


import React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ContentBlock from './ContentBlock';

const BookReader = ({ book, onTextSelect, onCloseBook }) => {
  const title = String(book.metadata.title || 'Unknown Title');
  const author = String(book.metadata.creator || 'Unknown Author');

  return (
    <Box
      component="article"
      sx={{
        position: 'relative', // Needed for positioning the back button
        maxWidth: '800px',
        mx: 'auto',
        lineHeight: '1.8',
        fontFamily: 'Georgia, serif',
        p: 4,
        height: '100vh', // Make it take full height
        overflowY: 'auto', // Enable scrolling
        boxSizing: 'border-box', // Ensure padding is included in height
      }}
      onMouseUp={onTextSelect}
    >
      <IconButton
        onClick={onCloseBook}
        sx={{
          position: 'absolute',
          top: 24,
          left: 24,
          zIndex: 1,
        }}
      >
        <ArrowBackIcon />
      </IconButton>
      <Typography variant="h4" component="h1" gutterBottom sx={{ textAlign: 'center', mt: 2 }}>
        {title}
      </Typography>
      <Typography variant="h5" component="h2" fontStyle="italic" color="text.secondary" mb={6} sx={{ textAlign: 'center' }}>
        {author}
      </Typography>
      {book.content_blocks.map(block => (
        <ContentBlock key={block.block_id} block={block} />
      ))}
    </Box>
  );
};

export default BookReader;
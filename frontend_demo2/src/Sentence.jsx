import React from 'react';
import Typography from '@mui/material/Typography';

const Sentence = ({ sentence }) => {
  return (
    <Typography
      component="span"
      data-sentence-id={sentence.sentence_id}
      data-page-id={sentence.page_id}
      sx={{
        cursor: 'pointer',
        transition: 'background-color 0.3s',
        borderRadius: '4px',
        '&:hover': {
          backgroundColor: 'rgba(255, 255, 0, 0.2)', // Light yellow hover
        },
      }}
    >
      {sentence.text}{' '}
    </Typography>
  );
};

export default Sentence;

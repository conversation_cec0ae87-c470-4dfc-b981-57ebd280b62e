{"name": "frontend_demo2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@chakra-ui/react": "^3.24.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/material": "^5.15.0", "framer-motion": "^12.23.12", "react": "^19.1.1", "react-dom": "^19.1.1", "@mui/icons-material": "^5.15.0"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.1.2"}}
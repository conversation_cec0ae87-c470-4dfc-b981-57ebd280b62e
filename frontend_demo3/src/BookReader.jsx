import React, { useState, useEffect, useRef, useCallback } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import CircularProgress from '@mui/material/CircularProgress';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ContentBlock from './ContentBlock';

const BookReader = ({ book, onTextSelect, onCloseBook }) => {
  const title = String(book.metadata.title || 'Unknown Title');
  const author = String(book.metadata.creator || 'Unknown Author');

  const [currentPageIndex, setCurrentPageIndex] = useState(0);
  const [paginatedContentBlocks, setPaginatedContentBlocks] = useState([]);
  const [scale, setScale] = useState(0.8); // Start with a reasonable default
  const [isReady, setIsReady] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const pageRef = useRef(null);
  const measurementContainerRef = useRef(null);
  const blockRefs = useRef({});

  // Initial scale calculation
  useEffect(() => {
    calculateScale();
  }, [calculateScale]);

  // Calculate scale to fit page in viewport
  const calculateScale = useCallback(() => {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    const pageWidth = 600; // Fixed page width
    const pageHeight = 800; // Fixed page height

    // Responsive margin based on screen size
    const margin = viewportWidth < 768 ? 40 : 100;
    const availableWidth = viewportWidth - margin;
    const availableHeight = viewportHeight - margin;

    const scaleX = availableWidth / pageWidth;
    const scaleY = availableHeight / pageHeight;

    // Don't scale up beyond 100%, but allow smaller scales for mobile
    const newScale = Math.min(scaleX, scaleY, 1);
    const finalScale = Math.max(newScale, 0.3); // Minimum scale of 30%

    console.log('Scale calculation:', {
      viewportWidth,
      viewportHeight,
      availableWidth,
      availableHeight,
      scaleX,
      scaleY,
      finalScale
    });

    setScale(finalScale);
  }, []);

  // Paginate content based on available height
  const paginateContent = useCallback(() => {
    if (!book || !measurementContainerRef.current) {
      console.log('Missing book or measurement container');
      return;
    }

    // Simple pagination without height measurement for now
    const pages = [[]]; // First page is cover page
    let currentPage = [];
    const blocksPerPage = 5; // Simple fixed number for testing

    console.log('Starting simple pagination...');

    // Simple pagination: just put a few blocks per page
    book.content_blocks.forEach((block, index) => {
      if (currentPage.length >= blocksPerPage) {
        pages.push(currentPage);
        currentPage = [block];
      } else {
        currentPage.push(block);
      }
    });

    // Add the last page if it has content
    if (currentPage.length > 0) {
      pages.push(currentPage);
    }

    console.log('Simple pagination complete. Total pages:', pages.length);
    setPaginatedContentBlocks(pages);
    setIsReady(true);
  }, [book]);

  // Initialize and handle resize
  useEffect(() => {
    if (!book) {
      console.log('No book data available');
      return;
    }

    console.log('Book loaded:', book.metadata.title);
    console.log('Content blocks count:', book.content_blocks.length);

    calculateScale();

    // Wait for DOM to be ready, then paginate
    const timer = setTimeout(() => {
      console.log('Starting pagination...');
      paginateContent();
    }, 300);

    return () => {
      clearTimeout(timer);
    };
  }, [book, calculateScale, paginateContent]);

  // Separate effect for resize handling
  useEffect(() => {
    const handleResize = () => {
      console.log('Window resized, recalculating scale...');
      calculateScale();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [calculateScale]);

  // Navigation handlers with animation
  const handleNextPage = useCallback(() => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentPageIndex(prev => Math.min(prev + 1, paginatedContentBlocks.length - 1));
    setTimeout(() => setIsTransitioning(false), 300);
  }, [paginatedContentBlocks.length, isTransitioning]);

  const handlePrevPage = useCallback(() => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentPageIndex(prev => Math.max(prev - 1, 0));
    setTimeout(() => setIsTransitioning(false), 300);
  }, [isTransitioning]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (e.key === 'ArrowLeft' || e.key === 'ArrowUp' || e.key === 'PageUp') {
        e.preventDefault();
        handlePrevPage();
      } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown' || e.key === 'PageDown' || e.key === ' ') {
        e.preventDefault();
        handleNextPage();
      } else if (e.key === 'Home') {
        e.preventDefault();
        setCurrentPageIndex(0);
      } else if (e.key === 'End') {
        e.preventDefault();
        setCurrentPageIndex(paginatedContentBlocks.length - 1);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [handleNextPage, handlePrevPage, paginatedContentBlocks.length]);

  // Handle page click navigation
  const handlePageClick = useCallback((e) => {
    const rect = pageRef.current?.getBoundingClientRect();
    if (!rect) return;

    const clickX = e.clientX - rect.left;
    const pageWidth = rect.width;

    // Click on left half = previous page, right half = next page
    if (clickX < pageWidth / 2) {
      handlePrevPage();
    } else {
      handleNextPage();
    }
  }, [handleNextPage, handlePrevPage]);

  const currentPageContent = paginatedContentBlocks[currentPageIndex] || [];
  const isFirstPage = currentPageIndex === 0;

  if (!isReady) {
    return (
      <Box sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        bgcolor: '#f5f5f5',
        gap: 2
      }}>
        <CircularProgress size={40} />
        <Typography variant="h6" color="text.secondary">
          Preparing your book...
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Debug: Book loaded: {book ? 'Yes' : 'No'},
          Blocks: {book ? book.content_blocks.length : 0}
        </Typography>

        {/* Render measurement container even when not ready */}
        {book && (
          <Box
            ref={measurementContainerRef}
            sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: 500,
              visibility: 'hidden',
              pointerEvents: 'none',
              zIndex: -1,
              fontFamily: 'Georgia, serif',
              fontSize: '16px',
              lineHeight: '1.6'
            }}
          >
            {book.content_blocks.map(block => (
              <div
                key={block.block_id}
                ref={el => blockRefs.current[block.block_id] = el}
                style={{ display: 'block' }}
              >
                <ContentBlock block={block} />
              </div>
            ))}
          </Box>
        )}
      </Box>
    );
  }

  return (
    <Box
      sx={{
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        width: '100vw',
        bgcolor: '#f5f5f5',
        overflow: 'hidden',
        fontFamily: 'Georgia, serif',
      }}
      onMouseUp={onTextSelect}
    >
      {/* Close button */}
      <IconButton
        onClick={onCloseBook}
        sx={{
          position: 'absolute',
          top: 20,
          left: 20,
          zIndex: 10,
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          '&:hover': { bgcolor: 'rgba(255, 255, 255, 1)' }
        }}
      >
        <ArrowBackIcon />
      </IconButton>

      {/* Book Page */}
      <Box
        ref={pageRef}
        onClick={handlePageClick}
        sx={{
          width: 600,
          height: 800,
          bgcolor: 'white',
          color: '#333333', // Explicit dark text color
          boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
          borderRadius: 2,
          transform: `scale(${scale})`,
          transformOrigin: 'center center',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          position: 'relative',
          cursor: 'pointer',
          userSelect: 'none',
          transition: 'transform 0.3s ease-in-out', // Smooth scaling transition
        }}
      >
        {/* Cover Page */}
        {isFirstPage ? (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            p: 4
          }}>
            {book.metadata.cover_image_url ? (
              <img
                src={`http://127.0.0.1:8000${book.metadata.cover_image_url}`}
                alt={`${title} Cover`}
                style={{
                  maxWidth: '100%',
                  maxHeight: '70%',
                  objectFit: 'contain',
                  marginBottom: '20px'
                }}
              />
            ) : (
              <Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                textAlign: 'center'
              }}>
                <Typography variant="h3" sx={{ mb: 2, fontWeight: 'bold' }}>
                  {title}
                </Typography>
                <Typography variant="h5" sx={{ color: 'text.secondary' }}>
                  {author}
                </Typography>
              </Box>
            )}
          </Box>
        ) : (
          /* Content Pages */
          <Box sx={{
            p: 5,
            height: '100%',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
            opacity: isTransitioning ? 0.7 : 1,
            transition: 'opacity 0.3s ease-in-out',
            color: '#333333', // Explicit dark text color
            fontFamily: 'Georgia, serif'
          }}>
            <Box sx={{
              flex: 1,
              overflow: 'hidden',
              color: '#333333' // Ensure text color inheritance
            }}>
              {currentPageContent.map(block => (
                <ContentBlock key={block.block_id} block={block} />
              ))}
            </Box>
          </Box>
        )}
      </Box>



      {/* Page indicator and help */}
      <Box sx={{
        position: 'absolute',
        bottom: 20,
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 10,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: 1
      }}>
        <Typography
          variant="caption"
          sx={{
            bgcolor: 'rgba(255, 255, 255, 0.9)',
            px: 2,
            py: 0.5,
            borderRadius: 1,
            fontSize: { xs: '0.7rem', sm: '0.75rem' }
          }}
        >
          Page {currentPageIndex + 1} of {paginatedContentBlocks.length}
        </Typography>

        {scale < 0.8 && (
          <Typography
            variant="caption"
            sx={{
              bgcolor: 'rgba(0, 0, 0, 0.7)',
              color: 'white',
              px: 1.5,
              py: 0.5,
              borderRadius: 1,
              fontSize: '0.6rem',
              textAlign: 'center'
            }}
          >
            Use ← → keys or tap page edges to navigate
          </Typography>
        )}
      </Box>

      {/* Navigation Buttons */}
      <IconButton
        onClick={handlePrevPage}
        disabled={currentPageIndex === 0 || isTransitioning}
        sx={{
          position: 'absolute',
          left: { xs: 10, sm: 20 },
          top: '50%',
          transform: 'translateY(-50%)',
          zIndex: 10,
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          '&:hover': { bgcolor: 'rgba(255, 255, 255, 1)' },
          '&:disabled': { bgcolor: 'rgba(255, 255, 255, 0.5)' },
          width: { xs: 40, sm: 48 },
          height: { xs: 40, sm: 48 }
        }}
      >
        <ArrowBackIcon fontSize={scale < 0.6 ? "medium" : "large"} />
      </IconButton>

      <IconButton
        onClick={handleNextPage}
        disabled={currentPageIndex === paginatedContentBlocks.length - 1 || isTransitioning}
        sx={{
          position: 'absolute',
          right: { xs: 10, sm: 20 },
          top: '50%',
          transform: 'translateY(-50%)',
          zIndex: 10,
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          '&:hover': { bgcolor: 'rgba(255, 255, 255, 1)' },
          '&:disabled': { bgcolor: 'rgba(255, 255, 255, 0.5)' },
          width: { xs: 40, sm: 48 },
          height: { xs: 40, sm: 48 }
        }}
      >
        <ArrowForwardIcon fontSize={scale < 0.6 ? "medium" : "large"} />
      </IconButton>
    </Box>
  );
};

export default BookReader;
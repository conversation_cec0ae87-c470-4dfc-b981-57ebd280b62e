import React, { useState, useEffect, useRef } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'; // Import for next page button
import ContentBlock from './ContentBlock';

const BookReader = ({ book, onTextSelect, onCloseBook }) => {
  const title = String(book.metadata.title || 'Unknown Title');
  const author = String(book.metadata.creator || 'Unknown Author');

  const [currentPageIndex, setCurrentPageIndex] = useState(0);
  const [paginatedContentBlocks, setPaginatedContentBlocks] = useState([]);
  const pageRef = useRef(null); // Ref for the page content area to measure its height
  const [scale, setScale] = useState(1); // State for scaling the page
  const contentWrapperRef = useRef(null); // Ref for the actual content rendering area inside the page

  const blockMeasurementRefs = useRef({}); // To store refs for each content block within the measurement container
  const measurementRef = useRef(null); // Ref for the hidden measurement container
  const tempContentMeasureRef = useRef(null); // Ref for temporary content measurement
  const contentRefs = useRef({}); // To store refs for each content block

  useEffect(() => {
    if (!book || !pageRef.current) return;

    const paginateContent = () => {
      const newPaginatedContent = [[]]; // Initialize with an empty array for the title/author page
      let currentPageBlocks = [];
      let currentHeight = 0;

      // Calculate the available height for content within the page
      // Calculate the available height for content within the page
      let pageContentHeight = tempContentMeasureRef.current.clientHeight;

      if (pageContentHeight <= 0) {
        setPaginatedContentBlocks([[]]); // Still provide a title page
        setCurrentPageIndex(0);
        return;
      }

      // Measure heights of all content blocks after they are rendered (even if hidden)
      const blockHeights = new Map();
      book.content_blocks.forEach(block => {
        const blockElement = blockMeasurementRefs.current[block.block_id]; // Use blockMeasurementRefs
        if (blockElement) {
          blockHeights.set(block.block_id, blockElement.offsetHeight);
        }
      });

      book.content_blocks.forEach(block => {
        const blockHeight = blockHeights.get(block.block_id) || 0;
        if (blockHeight === 0) {
          console.warn(`Could not measure height for block: ${block.block_id}. It might not be rendered or has no height.`);
        }

        if (currentHeight + blockHeight > pageContentHeight && currentPageBlocks.length > 0) {
          newPaginatedContent.push(currentPageBlocks);
          currentPageBlocks = [block];
          currentHeight = blockHeight;
        } else {
          currentPageBlocks.push(block);
          currentHeight += blockHeight;
        }
      });

      if (currentPageBlocks.length > 0) {
        newPaginatedContent.push(currentPageBlocks);
      }
      setPaginatedContentBlocks(newPaginatedContent);
      setCurrentPageIndex(0); // Reset to first page on new book or resize
    };

    const calculateScale = () => {
      if (!pageRef.current) return;
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      const pageWidth = 800; // Fixed width of the page
      const pageHeight = pageWidth * (4 / 3); // Fixed height based on aspect ratio

      const scaleX = viewportWidth / pageWidth;
      const scaleY = viewportHeight / pageHeight;

      // Choose the smaller scale to ensure the page fits both width and height
      const newScale = Math.min(scaleX, scaleY) * 0.8; // Scale down more aggressively to ensure fit
      setScale(newScale);
    };

    calculateScale(); // Calculate initial scale
    window.addEventListener('resize', calculateScale); // Recalculate on resize

    const timeoutId = setTimeout(() => {
      paginateContent();
    }, 500);

    window.addEventListener('resize', paginateContent);
    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', paginateContent);
    };
  }, [book]); // Depend on book to re-paginate when book changes

  const handleNextPage = () => {
    setCurrentPageIndex(prevIndex => Math.min(prevIndex + 1, paginatedContentBlocks.length - 1));
  };

  const handlePrevPage = () => {
    setCurrentPageIndex(prevIndex => Math.max(prevIndex - 1, 0));
  };

  const currentPageContent = paginatedContentBlocks[currentPageIndex] || [];

  return (
    <Box
      component="article"
      sx={{
        position: 'relative', // Needed for positioning the back button
        mx: 'auto',
        lineHeight: '1.8',
        fontFamily: 'Georgia, serif',
        boxSizing: 'border-box', // Ensure padding is included in height
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh', // Take full viewport height
        width: '100vw', // Take full viewport width
        overflow: 'hidden', // Hide scrollbars
      }}
      onMouseUp={onTextSelect}
    >
      

      <IconButton
        onClick={onCloseBook}
        sx={{
          position: 'absolute',
          top: 24,
          left: 24,
          zIndex: 1,
        }}
      >
        <ArrowBackIcon />
      </IconButton>

      {/* Page Container with 4:3 Aspect Ratio */}
      <Box
        ref={pageRef}
        sx={{
          width: '800px', // Fixed width for the page
          aspectRatio: '3 / 4', // Width / Height = 3 / 4
          bgcolor: 'background.paper',
          boxShadow: 3,
          transform: `scale(${scale})`, // Apply the calculated scale
          transformOrigin: 'center center', // Scale from center
          pt: 12, // Padding top
          pb: 16, // Padding bottom
          pl: 10, // Padding left
          pr: 10, // Padding right
          overflowY: 'hidden', // No internal scrolling for the page
          display: 'flex',
          flexDirection: 'column',
          flexShrink: 0, // Prevent shrinking
          color: 'text.primary', // Set text color to primary
        }}
      >
        {currentPageIndex === 0 && (
          <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', flex: 1 }}>
            {book.metadata.cover_image_url && (
              <img 
                src={`http://127.0.0.1:8000${book.metadata.cover_image_url}`} 
                alt={`${title} Cover`} 
                style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'contain' }} 
              />
            )}
          </Box>
        )}
        

        {/* Hidden container for measuring content block heights and total content height */}
                <Box ref={measurementRef} sx={{ display: 'block', visibility: 'hidden', width: '100%', height: 'auto', minHeight: '10000px' }}>
          {book.content_blocks.map(block => (
            <div key={block.block_id} ref={el => (blockMeasurementRefs.current[block.block_id] = el)} style={{ display: 'block' }}>
              <ContentBlock block={block} />
            </div>
          ))}
        </Box>

        <Box ref={contentWrapperRef} sx={{ flexGrow: 1 }}> {/* Dedicated content area */}
          {currentPageContent.map(block => (
            <ContentBlock key={block.block_id} block={block} />
          ))}
        </Box>

        {/* Temporary Box to measure available content height */}
        <Box ref={tempContentMeasureRef} sx={{ flexGrow: 1, visibility: 'hidden', position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}>
          {/* This box will be used to measure the available height for content */}
        </Box>
      </Box> {/* Closing tag for pageRef Box */}

      {/* Debugging Info */}
      <Typography variant="caption" sx={{ position: 'absolute', bottom: 24, left: '50%', transform: 'translateX(-50%)', zIndex: 1 }}>
        Page: {currentPageIndex + 1} / {paginatedContentBlocks.length} | Content Height: {tempContentMeasureRef.current?.clientHeight}
      </Typography>

      {/* Navigation Buttons */}
      <IconButton
        onClick={handlePrevPage}
        disabled={currentPageIndex === 0}
        sx={{
          position: 'absolute',
          left: 24,
          top: '50%',
          transform: 'translateY(-50%)',
          zIndex: 1,
        }}
      >
        <ArrowBackIcon fontSize="large" />
      </IconButton>
      <IconButton
        onClick={handleNextPage}
        disabled={currentPageIndex === paginatedContentBlocks.length - 1}
        sx={{
          position: 'absolute',
          right: 24,
          top: '50%',
          transform: 'translateY(-50%)',
          zIndex: 1,
        }}
      >
        <ArrowForwardIcon fontSize="large" />
      </IconButton>
    </Box>
  );
};

export default BookReader;
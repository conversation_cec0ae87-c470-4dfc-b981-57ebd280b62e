import React from 'react';
import Box from '@mui/material/Box';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';

function BookSelector({ books, onSelectBook, selectedBookId }) {
  return (
    <Box sx={{ width: '100%', maxWidth: 360, bgcolor: 'background.paper', border: '1px solid #ddd', borderRadius: '4px' }}>
      <Typography variant="h6" sx={{ p: 2, bgcolor: '#f5f5f5', borderBottom: '1px solid #ddd' }}>
        Select a Book
      </Typography>
      <List>
        {books.map((book) => (
          <ListItem key={book.id} disablePadding>
            <ListItemButton
              selected={book.id === selectedBookId}
              onClick={() => onSelectBook(book.id)}
            >
              <ListItemText 
                primary={book.metadata?.title || 'Untitled'} 
                secondary={book.metadata?.creator || 'Unknown Author'} 
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );
}

export default BookSelector;
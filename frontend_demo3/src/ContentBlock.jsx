import React from 'react';
import Sentence from './Sentence';
import Box from '@mui/material/Box';

const ContentBlock = ({ block }) => {
  const { block_type, sentences, metadata } = block;

  // 1. Handle image blocks
  if (block_type === 'img_container' && metadata?.src) {
    const imageUrl = `http://127.0.0.1:8000${metadata.src}`;
    return (
      <Box sx={{ textAlign: 'center', maxHeight: '80%', overflowY: 'auto' }}>
        <img src={imageUrl} alt={metadata.alt || ''} style={{ maxWidth: '100%', height: 'auto', display: 'block', margin: '0 auto' }} />
      </Box>
    );
  }

  // 2. Handle text-based blocks that have content
  if (sentences && sentences.length > 0) {
    const Tag = block_type;
    const style = {};
    if (Tag === 'p') {
      style.textIndent = '2em';
      style.lineHeight = '1.8';
    } else if (Tag.startsWith('h')) {
      style.marginTop = '2em';
      style.marginBottom = '1em';
      style.fontWeight = 'bold';
    }
    return (
      <Box component={Tag} data-block-id={block.block_id} sx={style}>
        {sentences.map(sentence => (
          <Sentence key={sentence.sentence_id} sentence={sentence} />
        ))}
      </Box>
    );
  }

  // 3. Handle paragraph blocks with no sentences as intentional spacers
  if (block_type === 'p') {
    return <Box sx={{ height: '1em' }} />; // Render a visual spacer
  }

  // 4. For any other block types with no sentences (like 'div'), render an empty Box.
  return <Box />;
};

export default ContentBlock;

import React from 'react';
import Sentence from './Sentence';
import Box from '@mui/material/Box';

const ContentBlock = ({ block }) => {
  const { block_type, sentences, metadata } = block;

  // 1. Handle image blocks
  if (block_type === 'img_container' && metadata?.src) {
    const imageUrl = `http://127.0.0.1:8000${metadata.src}`;
    return (
      <Box sx={{
        textAlign: 'center',
        mb: 2,
        maxHeight: '300px',
        overflow: 'hidden'
      }}>
        <img
          src={imageUrl}
          alt={metadata.alt || ''}
          style={{
            maxWidth: '100%',
            maxHeight: '300px',
            height: 'auto',
            display: 'block',
            margin: '0 auto',
            objectFit: 'contain'
          }}
        />
      </Box>
    );
  }

  // 2. Handle text-based blocks that have content
  if (sentences && sentences.length > 0) {
    const Tag = block_type;
    const style = {
      wordWrap: 'break-word',
      overflowWrap: 'break-word',
      hyphens: 'auto',
      color: '#333333', // Explicit dark text color
    };

    if (Tag === 'p') {
      style.textIndent = '1.5em';
      style.lineHeight = '1.6';
      style.marginBottom = '1em';
      style.fontSize = '16px';
    } else if (Tag.startsWith('h')) {
      style.marginTop = '1.5em';
      style.marginBottom = '0.8em';
      style.fontWeight = 'bold';
      style.lineHeight = '1.4';
      style.color = '#222222'; // Slightly darker for headings

      if (Tag === 'h1') {
        style.fontSize = '24px';
      } else if (Tag === 'h2') {
        style.fontSize = '20px';
      } else if (Tag === 'h3') {
        style.fontSize = '18px';
      }
    }

    return (
      <Box component={Tag} data-block-id={block.block_id} sx={style}>
        {sentences.map(sentence => (
          <Sentence key={sentence.sentence_id} sentence={sentence} />
        ))}
      </Box>
    );
  }

  // 3. Handle paragraph blocks with no sentences as intentional spacers
  if (block_type === 'p') {
    return <Box sx={{ height: '0.8em' }} />; // Render a visual spacer
  }

  // 4. For any other block types with no sentences (like 'div'), render an empty Box.
  return <Box sx={{ minHeight: '0.2em' }} />;
};

export default ContentBlock;

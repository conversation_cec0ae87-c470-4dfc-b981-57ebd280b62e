import React, { useState, useEffect } from 'react';
import Popover from '@mui/material/Popover';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import ButtonGroup from '@mui/material/ButtonGroup';

const InteractionPopup = ({ selection, isOpen, onClose }) => {
  const [aiResponse, setAiResponse] = useState('');
  const tags = ['Summarize', 'Explain', 'Translate'];

  const handleTagClick = (tag) => {
    // Simulate an AI response based on the tag
    switch (tag) {
      case 'Summarize':
        setAiResponse('This is a simulated summary of the selected text. The old man, <PERSON>, has had a long streak of bad luck but remains hopeful.');
        break;
      case 'Explain':
        setAiResponse('This is a simulated explanation. "Salao" is a Cuban Spanish term for the worst kind of bad luck.');
        break;
      case 'Translate':
        setAiResponse('This is a simulated translation to a different language.');
        break;
      default:
        setAiResponse('Response for the selected tag.');
    }
  };

  // Reset AI response when the popup is closed or selection changes
  useEffect(() => {
    if (!isOpen) {
      setTimeout(() => setAiResponse(''), 300); // Delay reset to allow for exit animation
    }
  }, [isOpen]);

  // Popover needs an anchor element. We'll create a dummy one at the selection's position.
  const [anchorEl, setAnchorEl] = useState(null);

  useEffect(() => {
    if (isOpen && selection.position) {
      // Create a dummy div to serve as the anchor for the Popover
      const dummyDiv = document.createElement('div');
      dummyDiv.style.position = 'absolute';
      dummyDiv.style.left = `${selection.position.x}px`;
      dummyDiv.style.top = `${selection.position.y}px`;
      dummyDiv.style.width = `${selection.position.width}px`;
      dummyDiv.style.height = `${selection.position.height}px`;
      dummyDiv.style.pointerEvents = 'none'; // Prevent interaction
      document.body.appendChild(dummyDiv);
      setAnchorEl(dummyDiv);
    } else if (anchorEl) {
      // Clean up the dummy div when the popover closes
      try {
        document.body.removeChild(anchorEl);
      } catch (e) {
        // Element might already be removed
        console.warn('Anchor element already removed');
      }
      setAnchorEl(null);
    }
  }, [isOpen, selection.position]);

  return (
    <Popover
      open={isOpen}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'left',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'left',
      }}
      disableAutoFocus={true}
      disableEnforceFocus={true}
      disableRestoreFocus={true}
      PaperProps={{
        sx: {
          p: 2,
          minWidth: '350px',
          maxWidth: '450px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
          display: 'flex',
          flexDirection: 'column',
          gap: '1rem',
        },
      }}
      slotProps={{
        root: {
          'aria-hidden': false,
        }
      }}
    >
      <Typography variant="body2" color="text.secondary" mb={1}>
        <strong>You selected:</strong>
      </Typography>
      <Typography variant="body1" fontStyle="italic" sx={{ p: 1, bgcolor: 'grey.50', borderLeft: '4px solid primary.main' }}>
        {selection.text}
      </Typography>

      {aiResponse ? (
        <Box sx={{ p: 2, bgcolor: 'blue.50', borderRadius: '4px' }}>
          <Typography variant="body2" fontWeight="bold" mb={1}>AI Response:</Typography>
          <Typography variant="body1">{aiResponse}</Typography>
        </Box>
      ) : (
        <Box>
          <ButtonGroup variant="outlined" fullWidth>
            {tags.map(tag => (
              <Button key={tag} onClick={() => handleTagClick(tag)}>
                {tag}
              </Button>
            ))}
          </ButtonGroup>
          <TextField
            multiline
            rows={3}
            placeholder="Or ask a custom question..."
            fullWidth
            sx={{ mt: 2 }}
          />
        </Box>
      )}

      <Button onClick={onClose} sx={{ alignSelf: 'flex-end', mt: 2 }}>
        Close
      </Button>
    </Popover>
  );
};

export default InteractionPopup;

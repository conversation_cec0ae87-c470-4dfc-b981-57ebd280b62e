import React from 'react';
import Typography from '@mui/material/Typography';

const Sentence = ({ sentence }) => {
  return (
    <Typography
      component="span"
      data-sentence-id={sentence.sentence_id}
      data-page-id={sentence.page_id}
      sx={{
        cursor: 'text', // Changed to text cursor for better UX
        transition: 'background-color 0.3s',
        borderRadius: '4px',
        color: '#333333', // Explicit dark text color
        userSelect: 'text', // Ensure text can be selected
        '&:hover': {
          backgroundColor: 'rgba(255, 255, 0, 0.1)', // Lighter hover effect
        },
        '&::selection': {
          backgroundColor: 'rgba(255, 255, 0, 0.3)', // Selection highlight
        },
      }}
    >
      {sentence.text}{' '}
    </Typography>
  );
};

export default Sentence;

# dev/hooks/hookspecs.py
import pluggy

# 创建一个项目专属的标记，避免与其他使用pluggy的库冲突
hookspec = pluggy.HookspecMarker("read_agent")

@hookspec
def register_prompts() -> dict:
    """
    注册提示词模板。
    
    插件通过实现这个钩子，可以向系统提供一个或多个提示词。
    返回的应该是一个字典，键是提示词的名称（如 'gisting'），值是提示词模板字符串。
    
    :return: 一个包含提示词模板的字典。
    """

@hookspec
def process_page(page: object, detected_language: str):
    """
    对分页后的页面内容进行链式处理。
    
    :param page: 一个标准化的页面数据对象 (例如 Page)。插件应该直接修改此对象。
    :param detected_language: 程序检测出的页面内容语言 (例如 'zh' 或 'en')。
    """
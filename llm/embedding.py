# dev/llm/embedding.py
import requests
import time
import logging
from typing import List
import sys
import os

# 将项目根目录添加到路径中以便导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import LLM_CONFIG

logger = logging.getLogger(__name__)

def get_embedding(text: str, provider: str = None) -> List[float]:
    """为给定文本生成向量嵌入。"""
    if provider is None:
        provider = LLM_CONFIG.get('embedding_provider', 'ollama_embedding')
    
    provider_config = LLM_CONFIG['providers'].get(provider)
    if not provider_config:
        raise ValueError(f"未找到嵌入服务提供商 '{provider}' 的配置")

    api_url = provider_config['api_url']
    model = provider_config['model']
    max_retries = provider_config.get('max_retries', 3)

    headers = {'Content-Type': 'application/json'}
    
    # 根据提供商构建不同的payload
    if provider == 'lmstudio_embedding':
        payload = {
            'model': model,
            'input': text
        }
    else: # 默认为ollama的格式
        payload = {
            'model': model,
            'prompt': text
        }

    logger.debug(f"嵌入调用 - 提供商: {provider}, 模型: {model}, 最大重试次数: {max_retries}")

    for attempt in range(max_retries):
        try:
            response = requests.post(api_url, headers=headers, json=payload, timeout=120)
            response.raise_for_status()
            data = response.json()
            logger.debug(f"嵌入调用 - 尝试 {attempt + 1} 成功。")
            
            # 根据提供商解析不同的响应
            if provider == 'lmstudio_embedding':
                return data['data'][0]['embedding']
            else: # 默认为ollama的格式
                return data['embedding']
        except requests.exceptions.RequestException as e:
            logger.warning(f"请求嵌入API时出错 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(5) # 等待5秒后重试
        except (KeyError, IndexError) as e:
            logger.error(f"解析来自 '{provider}' 的嵌入响应时出错: {e} - 响应数据: {data}")
            break # 响应格式错误，不重试
        except Exception as e:
            logger.error(f"处理嵌入请求时发生意外错误: {e}")
            break # 发生其他错误则不重试

    raise Exception(f'在 {max_retries} 次尝试后，获取向量嵌入失败')
# dev/llm/prompt_manager.py
import pluggy
from hooks import hookspecs
from system_plugins import default_prompts

class PromptManager:
    def __init__(self):
        """初始化提示词管理器。"""
        self._plugin_manager = pluggy.PluginManager("read_agent")
        self._plugin_manager.add_hookspecs(hookspecs)
        self.templates = {}
        self._load_plugins()
        self._build_prompt_collection()

    def _load_plugins(self):
        """加载插件。"""
        self._plugin_manager.register(default_prompts)
        # 未来可以从 user_plugins 目录加载

    def _build_prompt_collection(self):
        """构建提示词集合。"""
        all_prompts = {}
        results = self._plugin_manager.hook.register_prompts()
        results.reverse() # 后注册的插件优先
        for prompt_dict in results:
            if prompt_dict:
                all_prompts.update(prompt_dict)
        self._templates = all_prompts

    def get_template(self, name: str) -> str:
        """获取指定名称的提示词模板。"""
        return self._templates.get(name)

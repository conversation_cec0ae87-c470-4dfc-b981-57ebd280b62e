# dev/main.py
"""
Read Agent 主程序入口。
负责初始化环境、管理数据库、处理新书籍、并启动交互式查询会话。
"""

from dotenv import load_dotenv
load_dotenv()

import sys
import os
import logging
import argparse

# 将项目根目录添加到sys.path，以便导入自定义模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

from storage.storage_manager import StorageManager
from processing import core_processor
from query.query_manager import QueryManager
from storage.ingestor import EpubIngestor

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("read_agent.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def sync_corpus(storage: StorageManager):
    """
    同步函数：扫描 corpus 目录并与数据库比对，处理任何新的书籍。
    
    此函数执行以下操作：
    1. 检查 corpus 目录是否存在。
    2. 获取文件系统中所有 .epub 文件。
    3. 获取数据库中已记录的所有书籍路径。
    4. 比较两者差异，找出新添加的书籍。
    5. 对新书籍进行解析和处理，存入数据库。
    
    Args:
        storage (StorageManager): 存储管理器实例，用于与数据库交互。
    """
    logger.info("--- 开始同步书籍语料库 ---")
    corpus_path = os.path.join(os.path.dirname(__file__), 'corpus')
    if not os.path.isdir(corpus_path):
        logger.warning(f"语料库目录不存在: {corpus_path}，跳过同步。")
        return

    # 1. 获取文件系统中的所有 .epub 文件
    try:
        disk_books = [
            os.path.abspath(os.path.join(corpus_path, f))
            for f in os.listdir(corpus_path)
            if f.endswith('.epub')
        ]
        logger.info(f"在 corpus 目录中发现 {len(disk_books)} 本 .epub 电子书。")
    except OSError as e:
        logger.error(f"无法读取语料库目录 {corpus_path}: {e}")
        return

    # 2. 获取数据库中记录的所有书籍路径
    db_books_paths = set(storage.get_all_book_source_paths())
    logger.info(f"数据库中已记录 {len(db_books_paths)} 本书。")

    # 3. 找出新书并处理
    new_books_found = 0
    for book_path in disk_books:
        if book_path not in db_books_paths:
            new_books_found += 1
            logger.info(f"\n[新书发现] 开始处理: {os.path.basename(book_path)}")
            try:
                # 步骤一：摄取书籍内容到数据库 (Layer 1: 结构化存储)
                logger.info("  [步骤 1/2] 摄取EPUB书籍内容...")
                ingestor = EpubIngestor(book_path, storage)
                ingestor.ingest() # 这将向数据库添加书籍、章节、内容块和句子
                book_id = ingestor.book_id # 获取摄取过程中生成的书籍ID
                logger.info("     - 摄取完成.\n")

                # 步骤二：处理书籍内容 (Layer 2: 分页、向量化、摘要)
                logger.info("  [步骤 2/2] 处理书籍内容 (分页、向量化、摘要)...")
                # core_processor.process_book 期望接收包含 'id' 键的书籍数据字典
                core_processor.process_book({'id': book_id}, storage)
                logger.info(f"     - 《{os.path.basename(book_path)}》处理和存储完成.\n")

            except Exception as e:
                logger.error(f"处理新书《{os.path.basename(book_path)}》时发生错误: {e}", exc_info=True)

    if new_books_found == 0:
        logger.info("没有发现需要处理的新书.\n")
    
    logger.info("--- 书籍语料库同步完成 ---")


def main():
    """
    主执行函数。
    
    程序流程：
    1. 解析命令行参数。
    2. 初始化存储管理器（根据参数决定是否重建数据库）。
    3. 调用同步函数处理新添加的书籍。
    4. 初始化查询管理器。
    5. 进入交互式查询循环，等待用户输入问题。
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="Read Agent: Process and query e-books.")
    parser.add_argument(
        '--rebuild',
        action='store_true',
        help='强制重新构建数据库，删除所有现有数据。'
    )
    args = parser.parse_args()

    # 使用 with 语句管理数据库连接的生命周期
    # StorageManager 会自动处理数据库的打开和关闭
    with StorageManager(rebuild_db=args.rebuild) as storage:
        # 处理 corpus 目录中新增的书籍
        sync_corpus(storage)
        
        logger.info("\n--- 所有书籍处理完成 ---")
        
        # 初始化查询管理器，准备接受用户查询
        query_manager = QueryManager(storage)
        
        # --- 交互式查询循环 ---
        print("\n\n=======================================")
        print(" Read Agent 已准备就绪。可以开始提问了。")
        print("=======================================")
        print(" (输入 'exit' 或 'quit' 退出程序)")

        while True:
            try:
                question = input("\n> 请输入你的问题: ")
                if question.lower() in ['exit', 'quit']:
                    logger.info("用户退出。")
                    break
                
                # 执行查询并获取结果
                result = query_manager.query(question)
                
                # 格式化并打印答案
                print("\n--- 答案 ---")
                print(result['answer'])
                
                # 格式化并打印参考来源
                print("\n--- 参考来源 ---")
                if result.get('enhanced_context'):
                    for i, context_item in enumerate(result['enhanced_context']):
                        print(f"  [{i+1}] (来自页面ID: {context_item.get('page_id', 'N/A')})")
                        # 打印高亮片段，并进行适当的缩进
                        snippet_lines = context_item.get('snippet', 'N/A').split('\n')
                        for line in snippet_lines:
                            print(f"    {line}")
                        print()
                else:
                    print("  未找到相关参考来源。")

            except KeyboardInterrupt:
                logger.info("检测到用户中断 (Ctrl+C)。正在退出...")
                break
            except Exception as e:
                logger.error(f"查询过程中发生错误: {e}", exc_info=True)
                print(f"处理您的问题时发生错误，请检查日志。")

    logger.info("\n--- 程序结束 ---")


if __name__ == "__main__":
    main()
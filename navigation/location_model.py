# dev/navigation/location_model.py
from dataclasses import dataclass, field
from typing import Dict, Optional

@dataclass
class Location:
    """
    一个统一的数据模型，用于在系统内部和前后端之间描述文本在书籍中的位置。
    """
    # --- 必要字段 (没有默认值) ---
    book_id: str
    chapter_id: str
    spine_index: int
    chapter_href: str
    text_snippet: str
    text_hash: str
    
    # --- 可选字段 (有默认值) ---
    page_id: Optional[str] = None
    sentence_id: Optional[str] = None
    element_hints: Dict[str, str] = field(default_factory=dict)
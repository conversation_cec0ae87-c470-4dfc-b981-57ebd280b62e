# dev/navigation/navigator.py
import json
import hashlib
import re
import logging
from typing import Optional, List, Dict, Tuple
from collections import defaultdict
import difflib
import sys
import os

# 将项目根目录添加到路径中以便导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from storage.storage_manager import StorageManager
from navigation.location_model import Location
from llm.embedding import get_embedding
from config import DEFAULT_CONFIG

logger = logging.getLogger(__name__)

# 从外部文件加载停用词列表
def load_stop_words(file_path: str) -> list:
    """
    从文件加载停用词列表
    
    Args:
        file_path: 停用词文件路径
        
    Returns:
        停用词列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        logger.warning(f"在 {file_path} 未找到停用词文件，使用空列表")
        return []
    except Exception as e:
        logger.error(f"从 {file_path} 加载停用词时出错: {e}")
        return []

# 加载停用词
STOP_WORDS = load_stop_words(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'stop_words.txt'))
logger.info(f"已加载 {len(STOP_WORDS)} 个停用词")

# 配置参数
RRF_K = 60  # Reciprocal Rank Fusion 参数K
SIMILARITY_THRESHOLD = 0.5  # 关键词搜索的相似度阈值
TEXT_SNIPPET_LENGTH = 250  # 文本片段长度
SEARCH_LIMIT = 10  # 搜索结果限制

class Navigator:
    def __init__(self, storage_manager: StorageManager):
        """初始化导航器。"""
        self.storage = storage_manager
        # 确保停用词已加载
        if not STOP_WORDS:
            logger.warning("停用词列表为空。关键词提取可能无法按预期工作。")

    def _extract_keywords(self, query_text: str) -> str:
        """
        从查询文本中提取关键词，移除停用词。
        
        Args:
            query_text: 输入的查询文本
            
        Returns:
            处理后的关键词字符串
        """
        # 如果没有停用词，则直接返回原查询文本
        if not STOP_WORDS:
            logger.debug("未加载停用词，返回原始查询文本")
            return query_text
            
        # 预编译正则表达式以提高性能
        stop_words_pattern = r'\b(' + '|'.join(re.escape(word) for word in STOP_WORDS) + r')\b'
        compiled_pattern = re.compile(stop_words_pattern, re.IGNORECASE)
        
        # 移除停用词
        keywords = compiled_pattern.sub('', query_text)
        
        # 清理多余的空格
        processed_keywords = " ".join(keywords.split())
        
        logger.debug(f"原始查询: '{query_text}' -> 提取关键词: '{processed_keywords}'")
        return processed_keywords

    def _get_page_id_for_sentence(self, sentence_id: str) -> Optional[str]:
        """
        根据句子ID获取页面ID。
        
        Args:
            sentence_id: 句子ID
            
        Returns:
            页面ID或None（如果未找到）
        """
        try:
            page = self.storage.get_page_by_sentence_id(sentence_id)
            return page['id'] if page else None
        except Exception as e:
            logger.error(f"获取句子 {sentence_id} 的页面ID时出错: {e}")
            return None

    def _get_page_id_for_summary(self, summary_id: str) -> Optional[str]:
        """
        根据摘要ID获取页面ID。
        
        Args:
            summary_id: 摘要ID
            
        Returns:
            页面ID或None（如果未找到）
        """
        try:
            processed_data = self.storage.get_processed_data_by_id(summary_id)
            return processed_data['page_id'] if processed_data else None
        except Exception as e:
            logger.error(f"获取摘要 {summary_id} 的页面ID时出错: {e}")
            return None

    def _fuse_results(self, results: Dict[str, List[str]], k: int = RRF_K) -> List[Tuple[str, float]]:
        """
        使用Reciprocal Rank Fusion (RRF)算法融合多个搜索结果。
        
        Args:
            results: 来自不同来源的搜索结果字典
            k: RRF算法的参数K
            
        Returns:
            融合后的结果列表，按分数排序
        """
        fused_scores = defaultdict(float)
        for source, ranked_ids in results.items():
            logger.debug(f"融合来源 {source} 的 {len(ranked_ids)} 个项目的结果")
            for i, doc_id in enumerate(ranked_ids):
                if doc_id:
                    rank = i + 1
                    score = 1 / (k + rank)
                    fused_scores[doc_id] += score
                    logger.debug(f"来源 {source} 的文档 {doc_id} 在排名 {rank} 处获得分数 {score:.6f}")
        
        sorted_results = sorted(fused_scores.items(), key=lambda item: item[1], reverse=True)
        logger.debug(f"融合完成。融合项目总数: {len(sorted_results)}")
        return sorted_results

    def _build_location_from_page(self, page: dict, book_id: str) -> Optional[Location]:
        """
        根据页面数据构建位置对象。
        
        Args:
            page: 页面数据字典
            book_id: 书籍ID
            
        Returns:
            Location对象或None（如果构建失败）
        """
        if not page:
            logger.warning("无法构建位置: 页面数据为空")
            return None
            
        try:
            start_sentence = self.storage.get_sentence_by_id(page['start_sentence_id'])
            if not start_sentence:
                logger.warning(f"无法构建位置: 未找到起始句子 {page['start_sentence_id']}")
                return None

            chapter = self.storage.get_chapter_by_id(page['chapter_id'])
            if not chapter:
                logger.warning(f"无法构建位置: 未找到章节 {page['chapter_id']}")
                return None

            snippet = page['content'][:TEXT_SNIPPET_LENGTH]
            snippet_hash = hashlib.sha256(snippet.encode('utf-8')).hexdigest()

            return Location(
                book_id=book_id,
                chapter_id=page['chapter_id'],
                page_id=page['id'],
                sentence_id=page['start_sentence_id'],
                spine_index=chapter['spine_index'],
                chapter_href=chapter['chapter_href'],
                element_hints=json.loads(start_sentence['element_hints']),
                text_snippet=snippet,
                text_hash=snippet_hash
            )
        except Exception as e:
            logger.error(f"从页面构建位置时出错: {e}", exc_info=True)
            return None

    def find_location_by_text(self, book_id: str, query_text: str, context_location: Optional[Location] = None) -> Optional[Location]:
        """
        通过文本查找位置，使用混合搜索策略（关键词搜索 + 向量搜索）。
        
        Args:
            book_id: 书籍ID
            query_text: 查询文本
            context_location: 上下文位置（可选）
            
        Returns:
            找到的位置对象或None
        """
        logger.info(f"开始混合搜索: '{query_text}'")
        chapter_id_to_search = context_location.chapter_id if context_location else None

        # --- 路径A: 关键词搜索 (快速路径) ---
        logger.info("尝试关键词搜索...")
        keywords = self._extract_keywords(query_text) or query_text
        logger.debug(f"提取关键词: '{keywords}'")
        
        candidate_pages = self.storage.find_pages_by_text(
            book_id=book_id, 
            query_text=keywords, 
            chapter_id=chapter_id_to_search,
            limit=SEARCH_LIMIT
        )

        # 如果在特定章节未找到结果，尝试在整个书籍中搜索
        if not candidate_pages and chapter_id_to_search:
            logger.info("在上下文章节中未找到结果，搜索整本书...")
            candidate_pages = self.storage.find_pages_by_text(
                book_id=book_id, 
                query_text=keywords, 
                limit=SEARCH_LIMIT
            )

        if candidate_pages:
            logger.info(f"关键词搜索找到 {len(candidate_pages)} 个候选结果。检查质量...")
            best_page = None
            highest_score = 0.0
            
            # 查找最佳匹配页面
            for page in candidate_pages:
                try:
                    similarity = difflib.SequenceMatcher(None, query_text, page['content']).ratio()
                    logger.debug(f"页面 {page['id']} 相似度分数: {similarity:.4f}")
                    if similarity > highest_score:
                        highest_score = similarity
                        best_page = page
                except Exception as e:
                    logger.error(f"计算页面 {page.get('id', 'unknown')} 相似度时出错: {e}")
            
            # 如果找到高质量匹配，立即返回
            if highest_score > SIMILARITY_THRESHOLD:
                logger.info(f"找到高质量关键词匹配 (分数: {highest_score:.4f})。提前返回。")
                return self._build_location_from_page(best_page, book_id)
            else:
                logger.info(f"找到关键词匹配但质量较低 (最佳分数: {highest_score:.4f})。继续向量搜索。")

        # --- 路径B: 多层向量搜索 (回退方案) ---
        logger.info("回退到多层向量搜索...")
        try:
            logger.debug("为查询文本生成嵌入向量...")
            query_vector = get_embedding(query_text)
            logger.debug(f"查询嵌入向量已生成，维度: {len(query_vector)}")
            
            # 并行执行三种向量搜索
            logger.debug("查询页面向量...")
            page_ids_from_content = self.storage.query_page_vectors(query_vector, book_id, n_results=SEARCH_LIMIT)
            logger.debug(f"从内容向量找到 {len(page_ids_from_content)} 个页面匹配")
            
            logger.debug("查询摘要向量...")
            summary_ids = self.storage.query_summary_vectors(query_vector, book_id, n_results=SEARCH_LIMIT)
            logger.debug(f"从摘要向量找到 {len(summary_ids)} 个页面匹配")
            
            logger.debug("查询句子向量...")
            sentence_ids = self.storage.query_sentence_vectors(query_vector, book_id, n_results=SEARCH_LIMIT)
            logger.debug(f"从句子向量找到 {len(sentence_ids)} 个页面匹配")

            # 获取页面ID
            logger.debug("将摘要ID映射到页面ID...")
            page_ids_from_summaries = [self._get_page_id_for_summary(sid) for sid in summary_ids]
            valid_summary_page_ids = [pid for pid in page_ids_from_summaries if pid]
            logger.debug(f"从摘要映射了 {len(valid_summary_page_ids)} 个有效页面ID")
            
            logger.debug("将句子ID映射到页面ID...")
            page_ids_from_sentences = [self._get_page_id_for_sentence(sid) for sid in sentence_ids]
            valid_sentence_page_ids = [pid for pid in page_ids_from_sentences if pid]
            logger.debug(f"从句子映射了 {len(valid_sentence_page_ids)} 个有效页面ID")
            
            # 准备融合数据
            all_results = {
                'content': page_ids_from_content,
                'summary': valid_summary_page_ids,
                'sentence': valid_sentence_page_ids
            }
            
            # 执行结果融合
            logger.debug("融合搜索结果...")
            fused_ranked_pages = self._fuse_results(all_results)
            logger.debug(f"融合完成，结果数: {len(fused_ranked_pages)}")

            if not fused_ranked_pages:
                logger.warning("RRF融合后未找到结果。")
                return None

            best_page_id, top_score = fused_ranked_pages[0]
            logger.info(f"通过融合找到最佳页面: {best_page_id}，RRF分数 {top_score:.4f}")
            
            # 获取最佳页面数据
            logger.debug(f"获取最佳结果 {best_page_id} 的页面数据...")
            best_page_data_list = self.storage.get_pages_by_ids([best_page_id])
            if not best_page_data_list:
                logger.warning(f"未找到最佳结果 {best_page_id} 的页面数据")
                return None

            return self._build_location_from_page(best_page_data_list[0], book_id)

        except Exception as e:
            logger.error(f"向量搜索回退过程中发生错误: {e}", exc_info=True)
            # 错误恢复：如果向量搜索失败，尝试返回关键词搜索的最佳结果（如果有）
            if candidate_pages and best_page:
                logger.info("由于向量搜索失败，回退到最佳关键词匹配。")
                return self._build_location_from_page(best_page, book_id)
            return None
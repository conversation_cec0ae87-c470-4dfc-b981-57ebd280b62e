#!/usr/bin/env python3
# dev/navigation/test_navigator.py

import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# 将项目根目录添加到路径中
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 使用绝对路径导入
from navigation.navigator import Navigator
from navigation.location_model import Location

class TestNavigator(unittest.TestCase):
    def setUp(self):
        """在每个测试方法之前设置测试夹具。"""
        self.mock_storage = Mock()
        self.navigator = Navigator(self.mock_storage)

    def test_extract_keywords(self):
        """测试 _extract_keywords 方法。"""
        # 测试英文文本 - "over" 现在被正确识别为停用词
        result = self.navigator._extract_keywords("the quick brown fox jumps over the lazy dog")
        self.assertEqual(result, "quick brown fox jumps lazy dog")
        
        # 测试中文文本 - "一个" 应该被移除，因为它在STOP_WORDS中
        result = self.navigator._extract_keywords("这是 一个 测试 的 句子")
        self.assertEqual(result, "这是 测试 句子")

    def test_get_page_id_for_sentence_success(self):
        """测试 _get_page_id_for_sentence 成功检索的情况。"""
        self.mock_storage.get_page_by_sentence_id.return_value = {'id': 'page123'}
        result = self.navigator._get_page_id_for_sentence('sentence456')
        self.assertEqual(result, 'page123')

    def test_get_page_id_for_sentence_not_found(self):
        """测试 _get_page_id_for_sentence 未找到句子的情况。"""
        self.mock_storage.get_page_by_sentence_id.return_value = None
        result = self.navigator._get_page_id_for_sentence('sentence456')
        self.assertIsNone(result)

    def test_get_page_id_for_summary_success(self):
        """测试 _get_page_id_for_summary 成功检索的情况。"""
        self.mock_storage.get_processed_data_by_id.return_value = {'page_id': 'page123'}
        result = self.navigator._get_page_id_for_summary('summary456')
        self.assertEqual(result, 'page123')

    def test_get_page_id_for_summary_not_found(self):
        """测试 _get_page_id_for_summary 未找到摘要的情况。"""
        self.mock_storage.get_processed_data_by_id.return_value = None
        result = self.navigator._get_page_id_for_summary('summary456')
        self.assertIsNone(result)

    def test_fuse_results(self):
        """测试 _fuse_results 方法。"""
        results = {
            'content': ['A', 'B', 'C'],
            'summary': ['B', 'C', 'D'],
            'sentence': ['C', 'D', 'E']
        }
        fused = self.navigator._fuse_results(results, k=60)
        self.assertIsInstance(fused, list)
        self.assertGreater(len(fused), 0)
        # 检查结果是否按分数降序排列
        scores = [score for _, score in fused]
        self.assertEqual(scores, sorted(scores, reverse=True))

    @patch('navigation.navigator.json')
    @patch('navigation.navigator.hashlib')
    def test_build_location_from_page_success(self, mock_hashlib, mock_json):
        """测试 _build_location_from_page 成功创建的情况。"""
        # 模拟依赖项
        mock_sentence = {'element_hints': '{}'}
        mock_chapter = {'spine_index': 1, 'chapter_href': 'chap1.html'}
        mock_json.loads.return_value = {}
        
        mock_hash_obj = Mock()
        mock_hash_obj.hexdigest.return_value = 'hash123'
        mock_hashlib.sha256.return_value = mock_hash_obj
        
        self.mock_storage.get_sentence_by_id.return_value = mock_sentence
        self.mock_storage.get_chapter_by_id.return_value = mock_chapter
        
        page_data = {
            'id': 'page123',
            'chapter_id': 'chapter456',
            'start_sentence_id': 'sentence789',
            'content': '这是用于构建位置的测试页面内容。'
        }
        
        result = self.navigator._build_location_from_page(page_data, 'book123')
        
        self.assertIsInstance(result, Location)
        self.assertEqual(result.book_id, 'book123')
        self.assertEqual(result.chapter_id, 'chapter456')
        self.assertEqual(result.page_id, 'page123')
        self.assertEqual(result.sentence_id, 'sentence789')

    def test_build_location_from_page_empty_input(self):
        """测试 _build_location_from_page 页面数据为空的情况。"""
        result = self.navigator._build_location_from_page(None, 'book123')
        self.assertIsNone(result)

    @patch('navigation.navigator.difflib')
    def test_find_location_by_text_keyword_match(self, mock_difflib):
        """测试 find_location_by_text 高质量关键词匹配的情况。"""
        # 模拟存储以返回候选页面
        mock_page = {
            'id': 'page123',
            'chapter_id': 'chapter456',
            'start_sentence_id': 'sentence789',
            'content': '这是一个测试页面内容。'
        }
        self.mock_storage.find_pages_by_text.return_value = [mock_page]
        
        # 模拟difflib返回高相似度
        mock_difflib.SequenceMatcher().ratio.return_value = 0.9
        
        # 模拟位置构建的依赖项
        mock_sentence = {'element_hints': '{}'}
        mock_chapter = {'spine_index': 1, 'chapter_href': 'chap1.html'}
        self.mock_storage.get_sentence_by_id.return_value = mock_sentence
        self.mock_storage.get_chapter_by_id.return_value = mock_chapter
        
        with patch('navigation.navigator.json') as mock_json, \
             patch('navigation.navigator.hashlib') as mock_hashlib:
            mock_json.loads.return_value = {}
            mock_hash_obj = Mock()
            mock_hash_obj.hexdigest.return_value = 'hash123'
            mock_hashlib.sha256.return_value = mock_hash_obj
            
            result = self.navigator.find_location_by_text('book123', '测试查询')
            
            self.assertIsInstance(result, Location)
            self.assertEqual(result.book_id, 'book123')

    @patch('navigation.navigator.get_embedding')
    def test_find_location_by_text_vector_search(self, mock_get_embedding):
        """测试 find_location_by_text 回退到向量搜索的情况。"""
        # 模拟存储以返回无关键词匹配
        self.mock_storage.find_pages_by_text.return_value = []
        
        # 模拟嵌入向量生成
        mock_get_embedding.return_value = [0.1] * 768  # 典型的嵌入向量大小
        
        # 模拟向量搜索结果
        self.mock_storage.query_page_vectors.return_value = ['page123']
        self.mock_storage.query_summary_vectors.return_value = []
        self.mock_storage.query_sentence_vectors.return_value = []
        
        # 模拟页面检索
        mock_page = {
            'id': 'page123',
            'chapter_id': 'chapter456',
            'start_sentence_id': 'sentence789',
            'content': '这是来自向量搜索的测试页面内容。'
        }
        self.mock_storage.get_pages_by_ids.return_value = [mock_page]
        
        # 模拟位置构建的依赖项
        mock_sentence = {'element_hints': '{}'}
        mock_chapter = {'spine_index': 1, 'chapter_href': 'chap1.html'}
        self.mock_storage.get_sentence_by_id.return_value = mock_sentence
        self.mock_storage.get_chapter_by_id.return_value = mock_chapter
        
        with patch('navigation.navigator.json') as mock_json, \
             patch('navigation.navigator.hashlib') as mock_hashlib:
            mock_json.loads.return_value = {}
            mock_hash_obj = Mock()
            mock_hash_obj.hexdigest.return_value = 'hash123'
            mock_hashlib.sha256.return_value = mock_hash_obj
            
            result = self.navigator.find_location_by_text('book123', '测试查询')
            
            self.assertIsInstance(result, Location)
            self.assertEqual(result.book_id, 'book123')

if __name__ == '__main__':
    unittest.main()
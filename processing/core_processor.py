# dev/processing/core_processor.py
"""
书籍内容核心处理器。
负责对已摄取到数据库的书籍内容执行一系列核心处理步骤，
包括：分页、句子向量化、页面处理（如摘要生成）、页面内容向量化。
这些步骤为后续的检索增强生成（RAG）查询提供必要的数据和索引。
"""

from typing import List, Dict, Optional, Tuple
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
import hashlib
import pluggy
import os

from llm.llm_interface import query_llm, clean_llm_response
from llm.embedding import get_embedding
from llm.prompt_manager import PromptManager
from config import DEFAULT_CONFIG, LANGUAGE_MAPPING, LLM_CONFIG, PROCESSING_CONFIG
from . import text_utils
from hooks import hookspecs
from system_plugins import default_processors
from .pipeline_objects import Page
from storage.storage_manager import StorageManager


logger = logging.getLogger(__name__)

# ------------------- 辅助函数 (保持公共或可作为静态方法) -------------------

prompt_manager = PromptManager()

def _process_page_in_thread(page_data: dict, detected_language: str, pm: pluggy.PluginManager) -> Page:
    """
    在独立线程中处理单个页面。此函数不应包含任何非线程安全的操作。

    Args:
        page_data (dict): 包含页面基本信息和预先准备好的'content'的字典。
        detected_language (str): 检测到的书籍内容语言。
        pm (pluggy.PluginManager): 插件管理器。

    Returns:
        Page: 处理完成的Page对象，包含原始内容和插件处理结果。
    """
    page_id = page_data['id']
    
    # page_data 字典现在由主线程传入时已包含'content'
    page_content = page_data.get('content', '')

    page_object = Page(
        page_id=page_id,
        book_id=page_data['book_id'],
        page_index=page_data['page_index'],
        start_sentence_id=page_data['start_sentence_id'],
        end_sentence_id=page_data['end_sentence_id'],
        content=page_content
    )
    
    logger.info(f"  - [线程 {os.getpid()}] 调用process_page钩子处理页面 {page_id}...")
    pm.hook.process_page(page=page_object, detected_language=detected_language)
    return page_object

def get_prompt_with_language(template_name: str, language: str, **kwargs) -> str:
    """
    根据指定的语言获取对应的提示词模板，并填充变量。

    Args:
        template_name (str): 提示词模板的名称。
        language (str): 目标语言代码（如 'zh', 'en'）。
        **kwargs: 传递给模板的其他变量。

    Returns:
        str: 格式化后的完整提示词字符串。

    Raises:
        ValueError: 如果找不到指定名称的模板。
    """
    template = prompt_manager.get_template(template_name)
    if not template:
        raise ValueError(f"未找到 '{template_name}' 提示词模板")
    language_instruction = LANGUAGE_MAPPING.get(language, 'English')
    return template.format(language=language_instruction, **kwargs)

def parse_pause_point(response: str) -> Optional[int]:
    """
    从LLM的分页决策响应中解析出建议的断点位置。

    Args:
        response (str): LLM返回的原始响应文本。

    Returns:
        Optional[int]: 解析出的断点句子索引，如果未找到则返回None。
    """
    response = clean_llm_response(response)
    try:
        # 尝试多种常见的断点标记格式
        match = re.search(r'Break point:\s*<(\d+)>', response, re.IGNORECASE)
        if match:
            return int(match.group(1))
        match = re.search(r'断点[:：]\s*<(\d+)>', response)
        if match:
            return int(match.group(1))
        match = re.search(r'<(\d+)>', response)
        if match:
            return int(match.group(1))
            
        # 尝试更通用的模式
        patterns = [
            r'(?:break point|断点)[:：]?\s*(\d+)',
            r'(?:选择|choose|select)[:：]?\s*(\d+)',
            r'(?:标签|label|tag)[:：]?\s*(\d+)'
        ]
        for pattern in patterns:
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                return int(match.group(1))
        return None
    except (ValueError, AttributeError):
        return None

# ------------------- 书籍处理器类 -------------------

class BookProcessor:
    """
    书籍内容处理器。
    管理对单本书籍执行完整核心处理流程的逻辑。
    """

    def __init__(self, book_data: dict, storage_manager: StorageManager):
        """
        初始化书籍处理器。

        Args:
            book_data (dict): 包含书籍信息的字典，必须包含 'id' 键。
            storage_manager (StorageManager): 数据库存储管理器实例。
        """
        self.book_data = book_data
        self.storage_manager = storage_manager
        self.book_id = None
        self.detected_language = None
        # 缓存本书所有句子的列表，供后续步骤使用
        self.all_sentences_in_book = []

        # 初始化插件系统
        self.pm = pluggy.PluginManager("read_agent")
        self.pm.add_hookspecs(hookspecs)
        self.pm.register(default_processors)

    def process(self):
        """处理书籍的主入口点，遵循分层处理模型。"""
        if not self._check_book_status():
            return

        # --- 开始核心处理 (Layer 2) ---
        self.storage_manager.update_book_status(self.book_id, 'core_processing')
        logger.info(f"\n--- 开始核心处理 (Layer 2) for book {self.book_id} ---")

        self._sync_chapters_and_sentences()
        if not self.all_sentences_in_book:
            logger.warning(f"未找到或加载此书籍 {self.book_id} 的句子。中止处理。")
            self.storage_manager.update_book_status(self.book_id, 'error_no_sentences')
            return

        # Layer 2: 基础语义层处理
        self._paginate_text()
        self._vectorize_sentences()
        self._vectorize_page_content() # 调整顺序后的L2任务

        # 标记 Layer 2 完成
        self.storage_manager.update_book_status(self.book_id, 'core_processed')
        logger.info(f"\n--- 核心处理 (Layer 2) 完成. Book {self.book_id} Status: core_processed ---")

        # Layer 3: 语义扩展层处理
        logger.info(f"\n--- 开始语义扩展处理 (Layer 3) for book {self.book_id} ---")
        self._run_page_processors() # 重命名并调整顺序后的L3任务
        logger.info(f"\n--- Book {self.book_id} 所有处理步骤完成 ---")

    def _check_book_status(self) -> bool:
        """检查书籍状态，确保可以进行核心处理。"""
        logger.info("\n[检查] 检查书籍状态...")
        
        if 'id' not in self.book_data:
            logger.error("Book data does not contain 'id'. Cannot proceed.")
            return False
        
        self.book_id = self.book_data['id']
        book_record = self.storage_manager.get_book_by_id(self.book_id)
        
        if not book_record:
            logger.error(f"Book with ID {self.book_id} not found in database. Cannot proceed.")
            return False
            
        self.detected_language = book_record['detected_language']
        logger.info(f"  - Book ID: {self.book_id}, Detected Language: {self.detected_language}")
        
        # 新的状态检查逻辑
        current_status = book_record['status']
        if current_status in ['core_processed', 'done']: # 'done' for legacy compatibility
            logger.info(f"  - Book {self.book_id} has already completed core processing (status: {current_status}). Skipping.")
            return False
        
        if current_status != 'ingested':
            logger.warning(f"  - Book {self.book_id} is not in 'ingested' state (current: {current_status}). Proceeding cautiously.")

        logger.info(f"  - Book {self.book_id} status: {current_status}. Proceeding with core processing.")
        return True

    def _sync_chapters_and_sentences(self):
        """
        从数据库同步并加载本书所有句子到内存中。
        这是后续所有处理步骤（分页、向量化等）的基础。
        """
        logger.info("\n[L2] 加载所有句子...")
        self.storage_manager.update_book_status(self.book_id, 'loading_sentences')

        # 从数据库获取所有句子，并按全局顺序排列
        self.all_sentences_in_book = self.storage_manager.get_sentences_for_book(self.book_id)
        
        if not self.all_sentences_in_book:
            logger.warning(f"   - 未找到书籍 {self.book_id} 的任何句子。请确保已运行 ingestor.py。")
        else:
            logger.info(f"   - 已加载 {len(self.all_sentences_in_book)} 个句子。")
        
        self.storage_manager.update_book_status(self.book_id, 'sentences_loaded')

    def _paginate_text(self):
        """
        对书籍中的句子进行逻辑分页。
        使用增量处理方式，避免重复处理已存在的页面。
        可能会调用LLM来辅助确定语义合理的分页断点。
        """
        logger.info("\n[L2] 增量分页句子...")
        self.storage_manager.update_book_status(self.book_id, 'paginating')

        # 获取数据库中已存在的页面，以支持断点续传
        existing_pages = self.storage_manager.get_all_pages_for_book(self.book_id)
        start_index = 0
        page_index_offset = 0

        if existing_pages:
            page_index_offset = len(existing_pages)
            # 找到最后一个页面，以确定从哪里继续
            last_page = max(existing_pages, key=lambda p: p['page_index'])
            last_saved_sentence_id = last_page['end_sentence_id']
            
            # 为当前所有句子构建ID到索引的映射，以便快速查找
            sentence_id_map = {s['id']: i for i, s in enumerate(self.all_sentences_in_book)}
            if last_saved_sentence_id in sentence_id_map:
                start_index = sentence_id_map[last_saved_sentence_id] + 1
                logger.info(f"找到 {len(existing_pages)} 个现有页面。从句子索引 {start_index} 继续分页。")
            else:
                logger.warning("无法找到最后保存的句子ID。重新分页所有内容。")
                # 如果找不到最后一个句子，从头开始
                start_index = 0
                page_index_offset = 0

        # 根据检测到的语言获取处理配置
        # --- 新增：标准化语言代码以增强健壮性 ---
        LANGUAGE_NORMALIZATION_MAP = {
            'zh-cn': 'zh', 'zh_CN': 'zh', 'zh-tw': 'zh', 'zh_TW': 'zh',
            'en-us': 'en', 'en_US': 'en', 'en_GB': 'en',
        }
        normalized_language = LANGUAGE_NORMALIZATION_MAP.get(self.detected_language.lower(), self.detected_language)
        if normalized_language not in DEFAULT_CONFIG:
            logger.error(f"标准化语言 '{normalized_language}' (来自数据库: '{self.detected_language}') 无效。回退到默认语言 'zh'。")
            normalized_language = 'zh' 
        config = DEFAULT_CONFIG[normalized_language]
        # --- 新增完毕 ---

        i = start_index
        # 使用独立的变量来跟踪当前要创建的页面索引
        current_page_index = page_index_offset 

        # 迭代处理剩余的句子，创建新的页面
        while i < len(self.all_sentences_in_book):
            page_sentences, next_i = self._create_page(self.all_sentences_in_book, i, config, self.detected_language)
            
            if not page_sentences:
                i = next_i
                continue

            # 提取页面的起止句子ID
            start_sentence_id = page_sentences[0]['id']
            end_sentence_id = page_sentences[-1]['id']

            # 将新创建的页面信息存入数据库
            self.storage_manager.add_page(
                book_id=self.book_id,
                page_index=current_page_index,
                start_sentence_id=start_sentence_id,
                end_sentence_id=end_sentence_id
            )
            logger.info(f"    - 添加页面 {current_page_index}。")

            i = next_i
            current_page_index += 1
        logger.info("   - 分页完成。")

    def _vectorize_sentences(self):
        """
        对书籍中的所有句子进行向量化处理。
        使用增量处理方式，避免重复向量化已处理的句子。
        向量存储在ChromaDB向量数据库中。
        """
        logger.info("\n[L2] 增量向量化句子...")
        self.storage_manager.update_book_status(self.book_id, 'vectorizing_sentences')

        # 获取已向量化的句子ID集合，以确定需要处理哪些句子
        vectorized_sentence_ids = self.storage_manager.get_vectorized_sentence_ids(self.book_id)
        sentences_to_vectorize = [s for s in self.all_sentences_in_book if s['id'] not in vectorized_sentence_ids]
        
        logger.info(f"找到 {len(self.all_sentences_in_book)} 个句子。已向量化: {len(vectorized_sentence_ids)}。需要向量化: {len(sentences_to_vectorize)}。")

        # 对需要向量化的句子进行处理
        if sentences_to_vectorize:
            for sentence_data in sentences_to_vectorize:
                try:
                    # 调用嵌入服务生成句子向量
                    vector = get_embedding(sentence_data['content'])
                    # 存储向量，并附带元数据（book_id, chapter_id, block_id）
                    self.storage_manager.add_sentence_vector(
                        sentence_data['id'], vector, 
                        {"book_id": self.book_id, "chapter_id": sentence_data['chapter_id'], "block_id": sentence_data['block_id']}
                    )
                    logger.info(f"  - 句子 {sentence_data['id']} 的向量已添加。")
                except Exception as e:
                    logger.error(f"句子 {sentence_data['id']} 向量化失败: {e}")
        logger.info("   - 句子向量化完成。")

    def _run_page_processors(self):
        """[L3] 并发运行页面处理器（如摘要），并向量化其结果。"""
        logger.info("\n[L3] 并发处理页面扩展（如摘要）...")
        self.storage_manager.update_book_status(self.book_id, 'processing_extensions')
        
        all_pages = self.storage_manager.get_all_pages_for_book(self.book_id)
        processed_ids = self.storage_manager.get_processed_page_ids(self.book_id, 'gisting')
        pages_to_process = [p for p in all_pages if p['id'] not in processed_ids]
        logger.info(f"找到 {len(all_pages)} 个页面。已处理: {len(processed_ids)}。需要处理: {len(pages_to_process)}。")

        if pages_to_process:
            # 在主线程中为所有页面准备内容，避免跨线程数据库访问
            logger.info("在主线程中为所有页面准备内容...")
            pages_with_content = []
            for page_data in pages_to_process:
                sentences = self.storage_manager.get_sentences_for_page(page_data['id'])
                page_data['content'] = "\n".join([s['content'] for s in sentences])
                pages_with_content.append(page_data)
            logger.info("所有页面内容准备完毕。")

            max_workers = PROCESSING_CONFIG.get('max_workers', 5)
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交任务到线程池，不再传递 storage_manager
                futures = [executor.submit(_process_page_in_thread, page_data, self.detected_language, self.pm) for page_data in pages_with_content]
                for future in as_completed(futures):
                    try:
                        processed_page_object = future.result()
                        if processed_page_object and processed_page_object.processed_data:
                            for processor_name, result_data in processed_page_object.processed_data.items():
                                processed_data_id = self.storage_manager.add_processed_data(
                                    processed_page_object.page_id, processor_name, result_data)
                                logger.info(f"    - [主线程] 保存 '{processor_name}' 的结果，页面 {processed_page_object.page_id}。")
                                if processor_name == 'gisting':
                                    try:
                                        summary_vector = get_embedding(result_data)
                                        self.storage_manager.add_summary_vector(processed_data_id, summary_vector, {"book_id": self.book_id, "page_id": processed_page_object.page_id})
                                    except Exception as e:
                                        logger.error(f"    - [主线程] 页面 {processed_page_object.page_id} 的摘要向量化失败: {e}")
                    except Exception as exc:
                        logger.error(f'页面处理任务产生异常: {exc}', exc_info=True)
        logger.info("   - 页面扩展处理完成。")

    def _vectorize_page_content(self):
        """
        [L2] 对逻辑页面的内容进行向量化。
        页面内容是动态从其包含的句子重建的。
        使用增量处理方式，避免重复向量化。
        """
        logger.info("\n[L2] 向量化页面内容...")
        self.storage_manager.update_book_status(self.book_id, 'vectorizing_pages')
        
        all_pages = self.storage_manager.get_all_pages_for_book(self.book_id)
        vectorized_page_ids = self.storage_manager.get_vectorized_page_ids(self.book_id)
        pages_to_vectorize = [p for p in all_pages if p['id'] not in vectorized_page_ids]
        logger.info(f"找到 {len(all_pages)} 个页面。已向量化: {len(vectorized_page_ids)}。需要向量化: {len(pages_to_vectorize)}。")

        if pages_to_vectorize:
            for page_data in pages_to_vectorize:
                try:
                    # 根据页面定义的起止句子ID，重建页面内容
                    sentences_in_page = self.storage_manager.get_sentences_for_page(page_data['id'])
                    page_content = "\n".join([s['content'] for s in sentences_in_page])

                    vector = get_embedding(page_content)
                    self.storage_manager.add_page_vector(page_data['id'], vector, {"book_id": self.book_id, "page_index": page_data['page_index']})
                    logger.info(f"  - 页面 {page_data['id']} 的向量已添加。")
                except Exception as e:
                    logger.error(f"页面 {page_data['id']} 向量化失败: {e}")
        logger.info("   - 页面内容向量化完成。")

    @staticmethod
    def _create_page(sentences: List[Dict], start_idx: int, config: Dict, language: str) -> Tuple[List[Dict], int]:
        """
        创建一个逻辑页面，包含一组连续的句子。
        可能会调用LLM来决定最佳的分页断点，以保持语义连贯性。

        Args:
            sentences (List[Dict]): 全书所有句子的列表。
            start_idx (int): 当前页面开始的句子索引。
            config (Dict): 与语言相关的处理配置。
            language (str): 句子内容的语言。

        Returns:
            Tuple[List[Dict], int]: 
                - 第一个元素是构成当前页面的句子列表。
                - 第二个元素是下一个页面应开始的句子索引。
        """
        unit_limit = config.get('unit_limit', 1000) # 页面最大单位数
        start_threshold = config.get('start_threshold', 300) # 开始寻找断点的单位阈值

        # 初始化页面句子列表和单位计数器
        page_sentences_with_markers = [sentences[start_idx]['content']]
        unit_count = text_utils.count_text_units(sentences[start_idx]['content'], language)
        j = start_idx + 1

        # 累加句子，直到达到单位上限或没有更多句子
        while unit_count < unit_limit and j < len(sentences):
            segment_units = text_utils.count_text_units(sentences[j]['content'], language)
            unit_count += segment_units

            # 当累计单位数超过阈值时，插入一个标记，供LLM决策
            if unit_count >= start_threshold:
                page_sentences_with_markers.append(f"<{j}>")

            page_sentences_with_markers.append(sentences[j]['content'])
            j += 1

        # 在末尾插入一个标记
        page_sentences_with_markers.append(f"<{j}>")

        # 如果总单位数太少，则直接返回，无需LLM介入
        min_units = 100 if language == 'zh' else 50
        if unit_count < min_units:
            return sentences[start_idx:j], j

        # 调用LLM进行分页决策
        try:
            # 构造上下文：前文、当前段落（带标记）、后文预览
            preceding_context = ("" if start_idx == 0 else "...\n" +
                '\n'.join([s['content'] for s in sentences[max(0, start_idx-2):start_idx]]))
            next_preview = "" if j >= len(sentences) else sentences[j]['content'] + "\n..."

            # 获取并格式化提示词
            prompt = get_prompt_with_language(
                'pagination',
                language,
                previous_context=preceding_context,
                current_passage='\n'.join(page_sentences_with_markers),
                next_preview=next_preview
            )
            # 调用LLM API
            response = query_llm(
                prompt,
                temperature=config.get('temperature', 0.0),
                provider=config.get('provider', LLM_CONFIG.get('default_provider', 'ollama'))
            )
            
            # 清理LLM响应并解析断点
            cleaned_response = clean_llm_response(response)
            pause_point = parse_pause_point(cleaned_response)

            # 如果解析到有效断点，则使用该断点
            if pause_point and start_idx < pause_point <= j:
                return sentences[start_idx: pause_point], pause_point
            else:
                # 否则，使用默认的结尾位置
                return sentences[start_idx: j], j
        except Exception as e:
            # 如果LLM调用失败，记录警告并使用默认切分
            if config.get('verbose', False):
                logger.warning(f"分页决策失败，使用默认切分: {e}")
            return sentences[start_idx: j], j

# ------------------- 公共API函数 -------------------

def process_book(book_data: dict, storage_manager: StorageManager):
    """
    初始化并运行书籍处理流水线的公共入口函数。
    供模块外部调用，以启动对指定书籍的处理。

    Args:
        book_data (dict): 包含书籍信息的字典，必须包含 'id' 键。
        storage_manager (StorageManager): 数据库存储管理器实例。
    """
    processor = BookProcessor(book_data, storage_manager)
    processor.process()

# dev/processing/pipeline_objects.py
"""
处理流水线中的数据对象定义。
这些对象用于在处理流程的不同阶段之间传递数据。
"""

from dataclasses import dataclass, field
from typing import Dict, Any

@dataclass
class Page:
    """
    一个在处理流水线中传递的数据对象，代表一个逻辑页面及其处理结果。
    页面由一组连续的句子定义（通过起止句子ID）。
    """

    # 页面的唯一标识符 (UUID)
    page_id: str

    # 所属书籍的唯一标识符 (UUID)
    book_id: str

    # 页面在书籍中的索引 (从0开始)
    page_index: int

    # 定义此页面的起始句子ID (UUID)
    start_sentence_id: str

    # 定义此页面的结束句子ID (UUID)
    end_sentence_id: str

    # 页面的文本内容，由其包含的所有句子拼接而成
    content: str
    
    # 用于存储插件处理结果的字典。
    # 键是处理器名称 (如 'gisting' 用于摘要生成), 值是该处理器的处理结果。
    processed_data: Dict[str, Any] = field(default_factory=dict)
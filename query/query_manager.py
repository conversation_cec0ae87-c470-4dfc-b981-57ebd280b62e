# dev/query/query_manager.py
from config import LANGUAGE_MAPPING
from processing.text_utils import detect_language
from llm.prompt_manager import PromptManager
from llm.llm_interface import query_llm, clean_llm_response
from llm.embedding import get_embedding
from storage.storage_manager import StorageManager
from numpy.linalg import norm
import numpy as np
import os
import sys
from typing import List, Dict, Optional, Any
import logging
print("DEBUG: query/query_manager.py is being loaded.")

# 将项目根目录添加到路径中以便导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


logger = logging.getLogger(__name__)

logger.setLevel(logging.DEBUG)


class QueryManager:
    """
    管理RAG系统的整个查询过程。
    """

    def __init__(self, storage_manager: StorageManager):
        """
        初始化QueryManager。

        Args:
            storage_manager: StorageManager的一个实例，用于与数据库交互。
        """
        self.storage_manager = storage_manager
        self.prompt_manager = PromptManager()
        logger.info("QueryManager初始化完成。")

    def find_best_snippet_for_page(self, page: Dict[str, Any], query_vector: np.ndarray, context_window: int = 1) -> str:
        """
        在页面中找到最相关的句子并返回其周围的片段。
        """
        page_id = page['id']

        # 1. 获取该页面的所有句子
        sentences_in_page = self.storage_manager.get_sentences_for_page(
            page_id)
        if not sentences_in_page:
            # 如果没有句子，返回页面内容的截断作为降级方案
            return page['content'][:200].strip() + "..."

        # 2. 获取这些句子的向量
        sentence_ids = [s['id'] for s in sentences_in_page]
        try:
            # 从ChromaDB批量获取向量
            vectors_data = self.storage_manager.sentence_collection.get(
                ids=sentence_ids, include=["embeddings"])
            # 安全检查：是否有返回 embedding 数据
            embeddings = vectors_data.get('embeddings')
            if not vectors_data or embeddings is None or (isinstance(embeddings, np.ndarray) and embeddings.size == 0):
                raise ValueError("ChromaDB未返回嵌入向量")

            # 确保 embeddings 是二维 numpy 数组
            if isinstance(embeddings, np.ndarray):
                if embeddings.ndim == 1:
                    embeddings = embeddings[np.newaxis, :]
                elif embeddings.ndim != 2:
                    raise ValueError(
                        f"嵌入向量形状异常: {embeddings.shape}")
            else:
                embeddings = np.array(embeddings)

            # 构建句子向量字典
            sentence_vectors = {}
            for i, _id in enumerate(vectors_data['ids']):
                sentence_vectors[_id] = embeddings[i].flatten()

        except Exception as e:
            logger.error(
                f"无法获取页面 {page_id} 的句子向量: {e}")
            return page['content'][:200].strip() + "..."

        # 3. 计算与查询向量的相似度，找到最佳句子
        best_sentence_index = -1
        max_similarity = -1.0

        for i, sentence in enumerate(sentences_in_page):
            sentence_id = sentence['id']
            if sentence_id in sentence_vectors:
                s_vec = sentence_vectors[sentence_id]

                norm_query = norm(query_vector)
                norm_s = norm(s_vec)

                if norm_query == 0 or norm_s == 0:
                    logger.warning(
                        f"检测到句子 {sentence_id} 或查询的范数为零。跳过相似度计算。")
                    similarity = -1.0  # 分配一个低相似度
                else:
                    # 确保相似度是一个标量
                    similarity = float(
                        np.dot(query_vector, s_vec) / (norm_query * norm_s))

                if similarity > max_similarity:
                    max_similarity = similarity
                    best_sentence_index = i

        if best_sentence_index == -1:
            return page['content'][:200].strip() + "..."

        # 4. 构建上下文片段
        start_index = max(0, best_sentence_index - context_window)
        end_index = min(len(sentences_in_page),
                        best_sentence_index + context_window + 1)

        snippet_parts = []
        for i in range(start_index, end_index):
            sentence_text = sentences_in_page[i]['content']
            if i == best_sentence_index:
                # 高亮显示最匹配的句子
                snippet_parts.append(f"**>> {sentence_text} <<**")
            else:
                snippet_parts.append(sentence_text)

        return "\n".join(snippet_parts)

    def query(self, user_query: str, book_id: Optional[str] = None, n_results: int = 5) -> Dict[str, Any]:
        """
        使用多粒度检索和RRF进行排名的完整RAG查询。

        Args:
            user_query: 用户的问题。
            book_id: 可选的book_id以限制搜索范围。
            n_results: 要检索的最终上下文页面数。

        Returns:
            包含答案和源上下文的字典。
        """
        logger.info(f"收到查询，book_id '{book_id}': {user_query}")

        # 1. 获取用户查询的嵌入向量
        try:
            query_vector = get_embedding(user_query)
        except Exception as e:
            logger.error(f"获取查询嵌入向量失败: {e}")
            return {"answer": "抱歉，由于嵌入错误，我无法处理您的查询。", "context": [], "enhanced_context": []}

        # --- RRF多粒度检索 ---

        # 2. 初始化RRF分数并从所有粒度检索
        rrf_scores = {}
        k = 60  # RRF常数

        # 2.1. 页面级检索
        logger.info("执行页面级检索...")
        page_results = self.storage_manager.query_page_vectors(
            query_vector=query_vector, book_id=book_id, n_results=n_results * 2
        )
        if page_results and page_results.get('ids'):
            for rank, page_id in enumerate(page_results['ids'][0]):
                score = 1 / (k + rank)
                rrf_scores[page_id] = rrf_scores.get(page_id, 0) + score
            logger.info(
                f"RRF: 从页面级搜索添加了 {len(page_results['ids'][0])} 个分数。")

        # 2.2. 句子级检索
        logger.info("执行句子级检索...")
        sentence_results = self.storage_manager.query_sentence_vectors(
            query_vector=query_vector, book_id=book_id, n_results=n_results * 3
        )
        if sentence_results and sentence_results.get('ids'):
            # query_sentence_vectors的结果是一个字典，不是ID列表
            sentence_ids_list = sentence_results['ids'][0]
            for rank, sentence_id in enumerate(sentence_ids_list):
                page_data = self.storage_manager.get_page_by_sentence_id(
                    sentence_id)
                if page_data:
                    page_id = page_data['id']
                    score = 1 / (k + rank)
                    rrf_scores[page_id] = rrf_scores.get(page_id, 0) + score
            logger.info(
                f"RRF: 融合了 {len(sentence_ids_list)} 个句子的分数。")

        # 2.3. 摘要级检索
        logger.info("执行摘要级检索...")
        summary_results = self.storage_manager.query_summary_vectors(
            query_vector=query_vector, book_id=book_id, n_results=n_results * 2
        )
        if summary_results and summary_results.get('ids'):
            # 这些是processed_data_ids
            summary_ids = summary_results['ids'][0]
            for rank, processed_data_id in enumerate(summary_ids):
                processed_data = self.storage_manager.get_processed_data_by_id(
                    processed_data_id)
                if processed_data and processed_data.get('page_id'):
                    page_id = processed_data['page_id']
                    score = 1 / (k + rank)
                    rrf_scores[page_id] = rrf_scores.get(page_id, 0) + score
            logger.info(
                f"RRF: 融合了 {len(summary_ids)} 个摘要的分数。")

        # 3. 融合、排名并检索最终上下文
        if not rrf_scores:
            logger.warning(
                "多粒度检索后未找到与查询相关的上下文。")
            return {"answer": "我在书中找不到任何相关信息来回答您的问题。", "context": [], "enhanced_context": []}

        # 按最终RRF分数对页面ID进行排序
        sorted_page_ids = sorted(
            rrf_scores.keys(), key=lambda pid: rrf_scores[pid], reverse=True)

        final_page_ids = sorted_page_ids[:n_results]
        logger.debug(f"用于LLM的最终排名页面ID: {final_page_ids}")

        pages_data = self.storage_manager.get_pages_by_ids(final_page_ids)

        # 重新排序获取的页面以匹配RRF分数顺序
        page_map = {page['id']: page for page in pages_data}
        final_context_pages = [page_map[pid]
                               for pid in final_page_ids if pid in page_map]

        if not final_context_pages:
            logger.warning(
                "无法获取排名靠前的页面ID的内容。" ) 
            return {"answer": "我在书中找不到任何相关信息来回答您的问题。", "context": [], "enhanced_context": []}

        # 4. 为每个页面重建内容并格式化上下文
        for page in final_context_pages:
            sentences = self.storage_manager.get_sentences_for_page(page['id'])
            page['content'] = "\n".join([s['content'] for s in sentences])

        context_str = "\n\n---\n\n".join([page.get('content', '')
                                         for page in final_context_pages])

        prompt_template = self.prompt_manager.get_template('rag_qa')
        if not prompt_template:
            logger.error("找不到 'rag_qa' 提示模板。")
            return {"answer": "抱歉，我缺少回答问题的内部提示模板。", "context": final_context_pages, "enhanced_context": []}

        lang_code = detect_language(user_query)
        language = LANGUAGE_MAPPING.get(lang_code, 'English')

        final_prompt = prompt_template.format(
            context=context_str,
            query=user_query,
            language=language
        )

        # 5. 查询LLM获取最终答案
        try:
            raw_answer = query_llm(prompt=final_prompt)
            clean_answer = clean_llm_response(raw_answer)
        except Exception as e:
            logger.error(f"从LLM获取答案失败: {e}")
            return {"answer": "抱歉，在生成答案时遇到错误。", "context": final_context_pages, "enhanced_context": []}

        # 6. 将增强的上下文片段添加到结果中
        enhanced_context = []
        # 确保query_vector在传递前是1D numpy数组
        np_query_vector = np.array(query_vector).flatten()
        for page in final_context_pages:
            snippet = self.find_best_snippet_for_page(page, np_query_vector)
            enhanced_context.append({
                "page_id": page.get('id', 'N/A'),
                "snippet": snippet
            })

        logger.info(
            "成功生成答案和增强的上下文片段。")

        return {
            "answer": clean_answer,
            "context": final_context_pages,
            "enhanced_context": enhanced_context
        }

    def search_and_retrieve_sentences(self, user_query: str, book_id: Optional[str] = None, n_results: int = 10, context_window: int = 2) -> List[Dict[str, Any]]:
        """
        执行向量搜索以找到相关的句子并检索其周围的上下文。

        Args:
            user_query: 用户的查询。
            book_id: 可选的book_id以限制搜索范围。
            n_results: 要返回的相关句子数。
            context_window: 在匹配句子前后检索的句子数。

        Returns:
            字典列表，每个字典代表一个相关句子及其上下文。
        """
        logger.info(
            f"搜索book_id {book_id} 的 {n_results} 个相关句子")

        # 1. 获取用户查询的嵌入向量
        try:
            query_vector = get_embedding(user_query)
        except Exception as e:
            logger.error(f"获取查询嵌入向量失败: {e}")
            return []

        # 2. 从ChromaDB获取相关的句子ID
        sentence_results = self.storage_manager.query_sentence_vectors(
            query_vector=query_vector,
            book_id=book_id,
            n_results=n_results
        )

        if not sentence_results or not sentence_results.get('ids'):
            logger.warning("向量搜索未返回句子ID。")
            return []

        sentence_ids = sentence_results['ids'][0]
        logger.info(
            f"找到 {len(sentence_ids)} 个相关句子ID: {sentence_ids}")

        # 3. 获取句子详情并构建上下文
        results = []
        all_sentences_in_book = self.storage_manager.get_sentences_for_book(
            book_id)
        sentence_id_map = {s['id']: i for i,
                           s in enumerate(all_sentences_in_book)}

        for sentence_id in sentence_ids:
            if sentence_id in sentence_id_map:
                sentence_index = sentence_id_map[sentence_id]

                start_index = max(0, sentence_index - context_window)
                end_index = min(len(all_sentences_in_book),
                                sentence_index + context_window + 1)

                context_sentences = all_sentences_in_book[start_index:end_index]

                results.append({
                    "matched_sentence": all_sentences_in_book[sentence_index],
                    "context": context_sentences
                })

        logger.info(
            f"成功检索到 {len(results)} 个句子及其上下文。")
        return results

import zipfile
from bs4 import BeautifulSoup
from markdownify import markdownify as md
import os
import shutil
import urllib.parse
import xml.etree.ElementTree as ET

def slugify(text):
    """Converts text to a URL-friendly slug."""
    text = text.lower()
    text = text.replace(' ', '-')
    text = ''.join(char for char in text if char.isalnum() or char == '-')
    return text

def find_opf_path(temp_extract_dir):
    for root, _, files in os.walk(temp_extract_dir):
        for file in files:
            if file.endswith('.opf'):
                print(f"DEBUG: Found OPF at {os.path.join(root, file)}") # DEBUG PRINT
                return os.path.join(root, file)
    print("DEBUG: OPF not found.") # DEBUG PRINT
    return None

def find_nav_document_path(opf_path, temp_extract_dir):
    print(f"DEBUG: find_nav_document_path called with opf_path={opf_path}") # DEBUG PRINT
    try:
        tree = ET.parse(opf_path)
        root = tree.getroot()
        opf_ns = '{http://www.idpf.org/2007/opf}'

        # EPUB3 Nav Document
        for ref in root.findall(f'.//{opf_ns}manifest/{opf_ns}item'):
            if 'properties' in ref.attrib and 'nav' in ref.attrib['properties'].split():
                nav_path = os.path.join(os.path.dirname(opf_path), ref.attrib['href'])
                print(f"DEBUG: Found EPUB3 Nav Document: {nav_path}") # DEBUG PRINT
                return nav_path
        
        # EPUB2 NCX (fallback)
        spine_element = root.find(f'.//{opf_ns}spine')
        if spine_element is not None and 'toc' in spine_element.attrib:
            ncx_id = spine_element.attrib['toc']
            for item in root.findall(f'.//{opf_ns}manifest/{opf_ns}item'):
                if item.attrib.get('id') == ncx_id and item.attrib.get('media-type') == 'application/x-dtbncx+xml':
                    nav_path = os.path.join(os.path.dirname(opf_path), item.attrib['href'])
                    print(f"DEBUG: Found EPUB2 NCX: {nav_path}") # DEBUG PRINT
                    return nav_path

    except Exception as e:
        print(f"DEBUG: Error in find_nav_document_path: {e}") # DEBUG PRINT
    print("DEBUG: Nav document not found.") # DEBUG PRINT
    return None

def parse_ncx_toc(ncx_path):
    print(f"DEBUG: parse_ncx_toc called with ncx_path={ncx_path}") # DEBUG PRINT
    toc_entries = []
    try:
        tree = ET.parse(ncx_path)
        root = tree.getroot()
        ns = {'ncx': 'http://www.daisy.org/z3986/2005/ncx/'}
        
        for navpoint in root.findall('.//ncx:navPoint', ns):
            text_element = navpoint.find('ncx:navLabel/ncx:text', ns)
            content_element = navpoint.find('ncx:content', ns)
            
            if text_element is not None and content_element is not None:
                title = text_element.text
                href = content_element.attrib.get('src')
                if title and href:
                    toc_entries.append({'title': title, 'href': href})
        print(f"DEBUG: Parsed NCX toc_entries: {toc_entries}") # DEBUG PRINT
    except Exception as e:
        print(f"DEBUG: Error parsing NCX: {e}") # DEBUG PRINT
    return toc_entries

def parse_epub3_nav_toc(nav_path):
    print(f"DEBUG: parse_epub3_nav_toc called with nav_path={nav_path}") # DEBUG PRINT
    toc_entries = []
    try:
        with open(nav_path, 'r', encoding='utf-8') as f:
            soup = BeautifulSoup(f, 'html.parser')
            nav_ol = soup.find('nav', attrs={'epub:type': 'toc'}).find('ol')
            if nav_ol:
                for li in nav_ol.find_all('li', recursive=False):
                    a_tag = li.find('a')
                    if a_tag and a_tag.text and a_tag.get('href'):
                        toc_entries.append({'title': a_tag.text, 'href': a_tag.get('href')})
        print(f"DEBUG: Parsed EPUB3 Nav toc_entries: {toc_entries}") # DEBUG PRINT
    except Exception as e:
        print(f"DEBUG: Error parsing EPUB3 Nav: {e}") # DEBUG PRINT
    return toc_entries

def epub_to_markdown(epub_path, output_dir):
    book_name = os.path.splitext(os.path.basename(epub_path))[0]
    markdown_output_path = os.path.join(output_dir, f"{book_name}.md")
    
    temp_extract_dir = os.path.join(output_dir, f"temp_epub_extract_{book_name}")
    images_output_dir = os.path.join(output_dir, "images", book_name) # Dedicated directory for images

    # Ensure temp_extract_dir and images_output_dir are clean before extraction
    if os.path.exists(temp_extract_dir):
        shutil.rmtree(temp_extract_dir)
    os.makedirs(temp_extract_dir)

    if os.path.exists(images_output_dir):
        shutil.rmtree(images_output_dir)
    os.makedirs(images_output_dir)

    try:
        # 1. 解压 EPUB
        with zipfile.ZipFile(epub_path, 'r') as zf:
            zf.extractall(temp_extract_dir)
            
            # Extract images directly to their output directory
            for file_info in zf.infolist():
                if file_info.filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.svg')):
                    image_dest_path = os.path.join(images_output_dir, file_info.filename)
                    os.makedirs(os.path.dirname(image_dest_path), exist_ok=True)
                    with zf.open(file_info.filename) as source, open(image_dest_path, 'wb') as target:
                        shutil.copyfileobj(source, target)

        # Find and parse TOC
        opf_path = find_opf_path(temp_extract_dir)
        toc_entries = []
        if opf_path:
            nav_doc_path = find_nav_document_path(opf_path, temp_extract_dir)
            if nav_doc_path:
                if nav_doc_path.endswith('.ncx'):
                    toc_entries = parse_ncx_toc(nav_doc_path)
                elif nav_doc_path.endswith(('.html', '.xhtml')):
                    toc_entries = parse_epub3_nav_toc(nav_doc_path)
        
        print(f"DEBUG: toc_entries (after parsing) = {toc_entries}") # DEBUG PRINT

        # Generate Markdown TOC
        markdown_toc = ""
        if toc_entries:
            markdown_toc += "# Table of Contents\n\n"
            for entry in toc_entries:
                # Extract fragment part from href for the anchor
                # The href from NCX is just the file path, no fragment
                # So, we use slugified title as anchor
                anchor = slugify(entry['title'])
                markdown_toc += f"- [{entry['title']}](#{anchor})\n"
            markdown_toc += "\n---\n\n"
        
        print(f"DEBUG: markdown_toc (before final assembly) = {markdown_toc}") # DEBUG PRINT

        all_markdown_content = []

        # 2. 查找并解析 HTML 内容
        html_files_found = []
        for root, _, files in os.walk(temp_extract_dir):
            for file in files:
                if file.endswith(('.html', '.xhtml')):
                    html_files_found.append(os.path.join(root, file))
        
        html_files_found.sort() # Sort files to maintain chapter order if possible

        for html_file_path in html_files_found:
            # Skip if this HTML file was part of the navigation document itself
            if nav_doc_path and os.path.samefile(html_file_path, nav_doc_path):
                continue

            with open(html_file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # Remove unnecessary elements, like scripts, styles, and the XML declaration
                for script_or_style in soup(["script", "style"]):
                    script_or_style.decompose()

                # Extract only the body content to avoid XML declaration and head content
                body_content = soup.find('body')
                if body_content is None:
                    content_to_convert = soup
                else:
                    content_to_convert = body_content

                # Robust Original TOC Removal for part0000.html
                if os.path.basename(html_file_path) == 'part0000.html':
                    # Remove the specific p tags that form the TOC in part0000.html
                    for p_tag in content_to_convert.find_all('p'):
                        a_tag = p_tag.find('a')
                        if a_tag and a_tag.get('href', '').startswith('part') and '#filepos' in a_tag.get('href', ''):
                            p_tag.decompose()

                # Rewrite image paths
                for img_tag in content_to_convert.find_all('img'):
                    src = img_tag.get('src')
                    if src:
                        relative_html_path = os.path.relpath(html_file_path, temp_extract_dir)
                        full_src_path_in_epub = os.path.normpath(os.path.join(os.path.dirname(relative_html_path), src))
                        
                        image_relative_path_from_epub_root = full_src_path_in_epub
                        
                        new_src = os.path.join("images", book_name, image_relative_path_from_epub_root)
                        img_tag['src'] = new_src.replace(os.sep, '/') # Use forward slashes for URLs

                # Anchor Insertion for Headings
                # Find the corresponding TOC entry for this HTML file
                current_toc_entry = None
                for entry in toc_entries:
                    # Normalize paths for comparison
                    # The href from NCX is relative to the OPF file
                    abs_href_path = os.path.normpath(os.path.join(os.path.dirname(opf_path), entry['href']))
                    if abs_href_path == os.path.normpath(html_file_path):
                        current_toc_entry = entry
                        break
                
                markdown_chunk = md(str(content_to_convert))

                if current_toc_entry:
                    # Prepend raw HTML anchor to the markdown_chunk
                    # This is done after markdownify conversion, so it's directly in the Markdown string
                    anchor_id = slugify(current_toc_entry['title'])
                    markdown_chunk = f"<a id=\"{anchor_id}\"></a>\n" + markdown_chunk

                all_markdown_content.append(markdown_chunk)

        # 合并所有章节的 Markdown 内容，并在开头添加 TOC
        final_markdown = markdown_toc + "\n\n---\n\n".join(all_markdown_content) # 用分隔符连接章节

        # 写入 Markdown 文件
        with open(markdown_output_path, 'w', encoding='utf-8') as f:
            f.write(final_markdown)

        print(f"Converted '{epub_path}' to '{markdown_output_path}'")

    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        # For debugging, do not remove temp_extract_dir
        # if os.path.exists(temp_extract_dir):
        #     shutil.rmtree(temp_extract_dir)
        pass # Keep the directory for inspection

if __name__ == "__main__":
    # Define paths relative to the project root
    project_root = "/home/<USER>/read_agent_backend_01"
    epub_file = os.path.join(project_root, "corpus", "老人与海.epub")
    output_dir = os.path.join(project_root, "ref_docs", "output")
    
    epub_to_markdown(epub_file, output_dir)

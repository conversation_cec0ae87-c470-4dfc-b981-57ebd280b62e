import zipfile
from bs4 import BeautifulSoup
from markdownify import markdownify as md
import os
import shutil
import urllib.parse
import xml.etree.ElementTree as ET
import logging
import re

# --- Setup Logging ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class EpubToMarkdownConverter:
    """
    A class to convert EPUB files to Markdown, focusing on preserving structure,
    metadata, and internal links.
    """
    def __init__(self, epub_path, output_dir):
        self.epub_path = epub_path
        self.output_dir = output_dir
        self.book_name = os.path.splitext(os.path.basename(epub_path))[0]
        
        self.temp_extract_dir = os.path.join(self.output_dir, f"temp_epub_extract_{self.book_name}")
        self.images_output_dir = os.path.join(self.output_dir, "images", self.book_name)
        self.markdown_output_path = os.path.join(self.output_dir, f"{self.book_name}.md")

        self.opf_path = None
        self.opf_dir = None
        self.metadata = {}
        self.manifest = {}
        self.spine = []
        self.toc = []
        self.link_map = {}

    def convert(self):
        """Main conversion method."""
        try:
            self._prepare_directories()
            self._unzip_epub()
            self._parse_opf()
            self._parse_toc()
            self._build_link_map()
            
            markdown_content = self._process_spine_items()
            
            final_markdown = self._assemble_final_markdown(markdown_content)
            
            with open(self.markdown_output_path, 'w', encoding='utf-8') as f:
                f.write(final_markdown)
            
            logging.info(f"Successfully converted '{self.epub_path}' to '{self.markdown_output_path}'")

        except Exception as e:
            logging.error(f"An error occurred during conversion: {e}", exc_info=True)
        finally:
            # Clean up the temporary directory
            if os.path.exists(self.temp_extract_dir):
                 shutil.rmtree(self.temp_extract_dir)
                 logging.info(f"Cleaned up temporary directory: {self.temp_extract_dir}")

    def _prepare_directories(self):
        """Cleans and creates necessary directories."""
        for dir_path in [self.temp_extract_dir, self.images_output_dir]:
            if os.path.exists(dir_path):
                shutil.rmtree(dir_path)
            os.makedirs(dir_path)
        logging.info("Prepared clean directories.")

    def _unzip_epub(self):
        """Extracts the EPUB file content."""
        with zipfile.ZipFile(self.epub_path, 'r') as zf:
            zf.extractall(self.temp_extract_dir)
        logging.info(f"Unzipped EPUB to {self.temp_extract_dir}")

    def _find_opf_path(self):
        """Finds the .opf file in the extracted directory."""
        container_path = os.path.join(self.temp_extract_dir, "META-INF", "container.xml")
        if not os.path.exists(container_path):
            raise FileNotFoundError("META-INF/container.xml not found.")
        
        tree = ET.parse(container_path)
        root = tree.getroot()
        ns = {'n': "urn:oasis:names:tc:opendocument:xmlns:container"}
        
        rootfile_element = root.find('.//n:rootfile', namespaces=ns)
        if rootfile_element is None:
            raise FileNotFoundError("Could not find <rootfile> element in container.xml")
        
        opf_rel_path = rootfile_element.get('full-path')
        if not opf_rel_path:
            raise FileNotFoundError("Could not find full-path attribute in <rootfile> element")
            
        return os.path.join(self.temp_extract_dir, opf_rel_path)

    def _parse_opf(self):
        """Parses the .opf file to get metadata, manifest, and spine."""
        self.opf_path = self._find_opf_path()
        self.opf_dir = os.path.dirname(self.opf_path)
        
        tree = ET.parse(self.opf_path)
        root = tree.getroot()
        
        opf_ns = '{http://www.idpf.org/2007/opf}'
        dc_ns = '{http://purl.org/dc/elements/1.1/}'

        # Metadata
        metadata_elem = root.find(f'.//{opf_ns}metadata')
        if metadata_elem is not None:
            for elem in metadata_elem:
                # Use regex to remove namespace from tag
                tag = re.sub(r'\{.*?\}', '', elem.tag)
                self.metadata[tag] = elem.text
        
        # Manifest
        manifest_elem = root.find(f'.//{opf_ns}manifest')
        if manifest_elem is not None:
            for item in manifest_elem.findall(f'{opf_ns}item'):
                self.manifest[item.attrib['id']] = item.attrib['href']

        # Spine
        spine_elem = root.find(f'.//{opf_ns}spine')
        if spine_elem is not None:
            self.spine = [item.attrib['idref'] for item in spine_elem.findall(f'{opf_ns}itemref')]
            self.toc_ncx_id = spine_elem.attrib.get('toc')

        logging.info("Parsed .opf file successfully.")

    def _parse_toc(self):
        """Parses the Table of Contents (EPUB3 Nav or EPUB2 NCX)."""
        # Try EPUB3 Nav first
        nav_id = None
        for item_id in self.manifest:
            item_elem = self._get_manifest_item_by_id(item_id)
            if item_elem is not None and 'properties' in item_elem.attrib and 'nav' in item_elem.attrib['properties']:
                nav_id = item_id
                break
        
        if nav_id:
            nav_path_rel = self.manifest[nav_id]
            nav_path_abs = os.path.join(self.opf_dir, nav_path_rel)
            if os.path.exists(nav_path_abs):
                self._parse_epub3_nav(nav_path_abs)
                logging.info("Parsed EPUB3 Navigation Document.")
                return

        # Fallback to EPUB2 NCX
        if hasattr(self, 'toc_ncx_id') and self.toc_ncx_id and self.toc_ncx_id in self.manifest:
            ncx_path_rel = self.manifest[self.toc_ncx_id]
            ncx_path_abs = os.path.join(self.opf_dir, ncx_path_rel)
            if os.path.exists(ncx_path_abs):
                self._parse_ncx(ncx_path_abs)
                logging.info("Parsed EPUB2 NCX Table of Contents.")
                return
        
        logging.warning("No standard TOC found (EPUB3 Nav or NCX).")

    def _get_manifest_item_by_id(self, item_id):
        """Helper to find a manifest item element by its ID."""
        tree = ET.parse(self.opf_path)
        root = tree.getroot()
        opf_ns = '{http://www.idpf.org/2007/opf}'
        return root.find(f".//{opf_ns}manifest/{opf_ns}item[@id='{item_id}']")

    def _parse_epub3_nav(self, nav_path):
        with open(nav_path, 'r', encoding='utf-8') as f:
            soup = BeautifulSoup(f, 'html.parser')
        
        toc_nav = soup.find('nav', attrs={'epub:type': 'toc'})
        if not toc_nav:
            return

        for a_tag in toc_nav.find_all('a'):
            title = a_tag.text.strip()
            href = a_tag.get('href')
            if title and href:
                abs_href = os.path.join(os.path.dirname(nav_path), href)
                rel_href = os.path.relpath(abs_href, self.opf_dir)
                self.toc.append({'title': title, 'href': rel_href.replace(os.sep, '/')})

    def _parse_ncx(self, ncx_path):
        tree = ET.parse(ncx_path)
        root = tree.getroot()
        ns = {'ncx': 'http://www.daisy.org/z3986/2005/ncx/'}
        
        for navpoint in root.findall('.//ncx:navPoint', ns):
            text = navpoint.findtext('ncx:navLabel/ncx:text', namespaces=ns)
            href = navpoint.find('ncx:content', ns).attrib.get('src')
            if text and href:
                self.toc.append({'title': text.strip(), 'href': href})

    def _build_link_map(self):
        """Creates a map from original hrefs to new, clean Markdown anchors."""
        for i, entry in enumerate(self.toc):
            original_href = entry['href']
            new_anchor = f"section-{i+1}"
            self.link_map[original_href] = new_anchor
        logging.info(f"Built link map with {len(self.link_map)} entries.")

    def _process_spine_items(self):
        """Processes each HTML file in the spine order, converting it to Markdown."""
        all_markdown_content = []
        for idref in self.spine:
            if idref not in self.manifest:
                logging.warning(f"IDREF '{idref}' from spine not found in manifest. Skipping.")
                continue
            
            html_rel_path = self.manifest[idref]
            html_abs_path = os.path.join(self.opf_dir, html_rel_path)
            
            if not os.path.exists(html_abs_path):
                logging.warning(f"File '{html_abs_path}' not found. Skipping.")
                continue

            logging.info(f"Processing spine item: {html_rel_path}")
            with open(html_abs_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            self._cleanup_html(soup)
            
            # The Placeholder Method
            # 1. Find headings that are TOC targets, generate their MD, and replace them with a placeholder.
            manual_headings = self._extract_and_replace_headings(soup, html_rel_path)
            
            # 2. Rewrite all other links and image paths in the remaining soup.
            self._rewrite_links(soup, html_rel_path)
            self._rewrite_image_paths(soup, html_rel_path)
            
            # 3. Convert the rest of the soup to Markdown.
            markdown_chunk = md(str(soup.body or soup), heading_style="ATX")
            
            # 4. Replace placeholders with the manually generated, correct heading+anchor Markdown.
            for placeholder, heading_md in manual_headings.items():
                markdown_chunk = markdown_chunk.replace(placeholder, heading_md)
            
            all_markdown_content.append(markdown_chunk)
            
        return "\n\n---\n\n".join(all_markdown_content)

    def _extract_and_replace_headings(self, soup, current_html_rel_path):
        manual_headings = {}
        for original_href, new_anchor in self.link_map.items():
            href_file, _, href_fragment = original_href.partition('#')
            
            if href_file == current_html_rel_path and href_fragment:
                target_element = soup.find(id=href_fragment)
                if target_element:
                    heading_level = 1
                    if target_element.name.startswith('h') and len(target_element.name) == 2:
                        try:
                            heading_level = int(target_element.name[1])
                        except ValueError:
                            pass # Keep level 1
                    hashes = '#' * heading_level
                    heading_text = target_element.get_text().strip().replace('\n', ' ')
                    
                    placeholder = f"ANCHORPLACEHOLDER{new_anchor}"
                    
                    # This is the guaranteed-to-work robust anchor + heading
                    manual_md = f'<a id="{new_anchor}"></a>\n\n{hashes} {heading_text}\n'
                    
                    manual_headings[placeholder] = manual_md
                    target_element.replace_with(soup.new_string(placeholder))
        return manual_headings

    def _rewrite_links(self, soup, current_html_rel_path):
        for a_tag in soup.find_all('a', href=True):
            original_href = a_tag['href']
            resolved_href = os.path.normpath(os.path.join(os.path.dirname(current_html_rel_path), original_href))
            if resolved_href in self.link_map:
                a_tag['href'] = f"#{self.link_map[resolved_href]}"

    def _rewrite_image_paths(self, soup, current_html_rel_path):
        for img_tag in soup.find_all('img', src=True):
            src = img_tag.get('src')
            if not src: continue

            img_path_rel_to_opf = os.path.normpath(os.path.join(os.path.dirname(current_html_rel_path), src))
            img_src_abs = os.path.join(self.opf_dir, img_path_rel_to_opf)
            
            if os.path.exists(img_src_abs):
                img_dest_rel = os.path.join("images", self.book_name, img_path_rel_to_opf).replace(os.sep, '/')
                img_dest_abs = os.path.join(self.output_dir, img_dest_rel)
                
                os.makedirs(os.path.dirname(img_dest_abs), exist_ok=True)
                shutil.copy2(img_src_abs, img_dest_abs)
                
                img_tag['src'] = img_dest_rel
            else:
                logging.warning(f"Image not found: {img_src_abs}")

    def _cleanup_html(self, soup):
        """Removes script, style, and other unwanted tags from the soup."""
        for tag in soup(["script", "style"]):
            tag.decompose()

    def _assemble_final_markdown(self, markdown_content):
        """Assembles the YAML frontmatter, TOC, and main content."""
        frontmatter = "---" + "\n"
        for key, value in self.metadata.items():
            if value:
                processed_value = str(value).replace('"', '\"')
                frontmatter += f"{key}: \"{processed_value}\"\n"
        frontmatter += "---" + "\n\n"

        markdown_toc = "# Table of Contents" + "\n\n"
        for entry in self.toc:
            original_href = entry['href']
            if original_href in self.link_map:
                anchor = self.link_map[original_href]
                title = entry['title'].strip()
                markdown_toc += f"- [{title}](#{anchor})\n"
        markdown_toc += "\n---" + "\n\n"

        return frontmatter + markdown_toc + markdown_content

if __name__ == "__main__":
    project_root = "/home/<USER>/read_agent_backend_01"
    
    logging.info("--- Processing Network_Science.epub ---")
    epub_ns = os.path.join(project_root, "corpus", "Network_Science.epub")
    output_ns = os.path.join(project_root, "ref_docs", "output")
    converter_ns = EpubToMarkdownConverter(epub_ns, output_ns)
    converter_ns.convert()

    logging.info("\n--- Processing 老人与海.epub ---")
    epub_oldman = os.path.join(project_root, "corpus", "老人与海.epub")
    output_oldman = os.path.join(project_root, "ref_docs", "output")
    converter_oldman = EpubToMarkdownConverter(epub_oldman, output_oldman)
    converter_oldman.convert()
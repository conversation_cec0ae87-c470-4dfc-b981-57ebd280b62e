# backend/book_navigation.py
import sqlite3
import json
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from bs4 import BeautifulSoup
import ebooklib
from ebooklib import epub
from sentence_transformers import SentenceTransformer
import hashlib

@dataclass
class LocationInfo:
    method: str
    spine_index: int
    chapter_href: str
    search_text: str
    element_hints: Dict
    context_before: str
    context_after: str
    confidence: float

class BookNavigationService:
    def __init__(self, db_path: str = "book_index.db"):
        self.db_path = db_path
        self.init_database()
        # 用于语义搜索的模型
        self.semantic_model = SentenceTransformer('all-MiniLM-L6-v2')
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        conn.execute('''
            CREATE TABLE IF NOT EXISTS book_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                book_id TEXT NOT NULL,
                chapter_href TEXT NOT NULL,
                spine_index INTEGER NOT NULL,
                text_content TEXT NOT NULL,
                text_hash TEXT NOT NULL,
                start_offset INTEGER,
                end_offset INTEGER,
                element_tag TEXT,
                element_id TEXT,
                element_class TEXT,
                context_before TEXT,
                context_after TEXT,
                semantic_vector BLOB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.execute('''
            CREATE INDEX IF NOT EXISTS idx_book_text 
            ON book_positions (book_id, text_hash)
        ''')
        
        conn.execute('''
            CREATE INDEX IF NOT EXISTS idx_book_spine 
            ON book_positions (book_id, spine_index)
        ''')
        
        conn.commit()
        conn.close()
    
    def index_book(self, book_path: str, book_id: str = None) -> str:
        """索引整本书，返回book_id"""
        if not book_id:
            book_id = self._generate_book_id(book_path)
        
        # 检查是否已经索引过
        if self._is_book_indexed(book_id):
            return book_id
        
        book = epub.read_epub(book_path)
        conn = sqlite3.connect(self.db_path)
        
        try:
            spine_items = [(item, is_linear) for item, is_linear in book.spine]
            
            for spine_index, (item, is_linear) in enumerate(spine_items):
                if isinstance(item, str):
                    # 通过ID查找实际的item
                    actual_item = book.get_item_with_id(item)
                else:
                    actual_item = item
                
                if actual_item and actual_item.get_type() == ebooklib.ITEM_DOCUMENT:
                    self._index_chapter(conn, book_id, actual_item, spine_index)
            
            conn.commit()
            print(f"Successfully indexed book: {book_id}")
            
        except Exception as e:
            conn.rollback()
            print(f"Error indexing book: {e}")
            raise
        finally:
            conn.close()
        
        return book_id
    
    def _generate_book_id(self, book_path: str) -> str:
        """生成书籍ID"""
        return hashlib.md5(Path(book_path).name.encode()).hexdigest()[:12]
    
    def _is_book_indexed(self, book_id: str) -> bool:
        """检查书籍是否已被索引"""
        conn = sqlite3.connect(self.db_path)
        result = conn.execute(
            "SELECT COUNT(*) FROM book_positions WHERE book_id = ?", 
            (book_id,)
        ).fetchone()
        conn.close()
        return result[0] > 0
    
    def _index_chapter(self, conn: sqlite3.Connection, book_id: str, 
                      item: ebooklib.epub.EpubHtml, spine_index: int):
        """索引单个章节"""
        try:
            content = item.get_content().decode('utf-8', errors='ignore')
            soup = BeautifulSoup(content, 'html.parser')
            chapter_href = item.get_name()
            
            # 提取所有文本段落
            text_elements = soup.find_all(['p', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
            
            for element in text_elements:
                text = element.get_text().strip()
                if len(text) < 10:  # 过滤太短的文本
                    continue
                
                # 获取上下文
                context_before, context_after = self._get_context(element, 100)
                
                # 生成语义向量
                semantic_vector = self.semantic_model.encode([text])[0].tobytes()
                
                # 插入数据库
                conn.execute('''
                    INSERT INTO book_positions 
                    (book_id, chapter_href, spine_index, text_content, text_hash,
                     element_tag, element_id, element_class, context_before, 
                     context_after, semantic_vector)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    book_id, chapter_href, spine_index, text,
                    hashlib.md5(text.encode()).hexdigest(),
                    element.name,
                    element.get('id', ''),
                    ' '.join(element.get('class', [])),
                    context_before, context_after, semantic_vector
                ))
                
        except Exception as e:
            print(f"Error indexing chapter {item.get_name()}: {e}")
    
    def _get_context(self, element, context_length: int = 100) -> Tuple[str, str]:
        """获取元素的上下文"""
        # 简化实现，实际可以更复杂
        prev_text = ""
        next_text = ""
        
        # 获取前面的文本
        prev_sibling = element.previous_sibling
        while prev_sibling and len(prev_text) < context_length:
            if hasattr(prev_sibling, 'get_text'):
                prev_text = prev_sibling.get_text() + " " + prev_text
            prev_sibling = prev_sibling.previous_sibling if prev_sibling else None
        
        # 获取后面的文本  
        next_sibling = element.next_sibling
        while next_sibling and len(next_text) < context_length:
            if hasattr(next_sibling, 'get_text'):
                next_text = next_text + " " + next_sibling.get_text()
            next_sibling = next_sibling.next_sibling if next_sibling else None
        
        return prev_text[:context_length], next_text[:context_length]
    
    def find_target_location(self, target_book_id: str, 
                           search_text: str, 
                           context_hint: str = None) -> Optional[LocationInfo]:
        """查找目标位置，支持多种策略"""
        
        # 策略1：精确文本匹配
        exact_match = self._find_by_exact_text(target_book_id, search_text)
        if exact_match:
            return exact_match
        
        # 策略2：模糊文本匹配
        fuzzy_match = self._find_by_fuzzy_text(target_book_id, search_text)
        if fuzzy_match:
            return fuzzy_match
        
        # 策略3：语义匹配
        semantic_match = self._find_by_semantic(target_book_id, search_text)
        if semantic_match:
            return semantic_match
        
        # 策略4：关键词匹配
        keyword_match = self._find_by_keywords(target_book_id, search_text)
        return keyword_match
    
    def _find_by_exact_text(self, book_id: str, search_text: str) -> Optional[LocationInfo]:
        """精确文本匹配"""
        conn = sqlite3.connect(self.db_path)
        result = conn.execute('''
            SELECT * FROM book_positions 
            WHERE book_id = ? AND text_content = ?
            LIMIT 1
        ''', (book_id, search_text)).fetchone()
        conn.close()
        
        if result:
            return self._create_location_info(result, "exact_match", 1.0)
        return None
    
    def _find_by_fuzzy_text(self, book_id: str, search_text: str) -> Optional[LocationInfo]:
        """模糊文本匹配"""
        conn = sqlite3.connect(self.db_path)
        results = conn.execute('''
            SELECT * FROM book_positions 
            WHERE book_id = ? AND text_content LIKE ?
            ORDER BY LENGTH(text_content) ASC
            LIMIT 5
        ''', (book_id, f'%{search_text}%')).fetchall()
        conn.close()
        
        if results:
            # 选择最匹配的结果
            best_match = min(results, key=lambda x: abs(len(x[4]) - len(search_text)))
            confidence = self._calculate_text_similarity(search_text, best_match[4])
            return self._create_location_info(best_match, "fuzzy_match", confidence)
        return None
    
    def _find_by_semantic(self, book_id: str, search_text: str) -> Optional[LocationInfo]:
        """语义匹配"""
        search_vector = self.semantic_model.encode([search_text])[0]
        
        conn = sqlite3.connect(self.db_path)
        results = conn.execute('''
            SELECT * FROM book_positions 
            WHERE book_id = ? AND semantic_vector IS NOT NULL
        ''', (book_id,)).fetchall()
        conn.close()
        
        if not results:
            return None
        
        best_match = None
        best_score = -1
        
        for result in results:
            stored_vector = np.frombuffer(result[11], dtype=np.float32)
            similarity = np.dot(search_vector, stored_vector) / (
                np.linalg.norm(search_vector) * np.linalg.norm(stored_vector)
            )
            
            if similarity > best_score:
                best_score = similarity
                best_match = result
        
        if best_match and best_score > 0.7:  # 阈值可调
            return self._create_location_info(best_match, "semantic_match", best_score)
        return None
    
    def _find_by_keywords(self, book_id: str, search_text: str) -> Optional[LocationInfo]:
        """关键词匹配"""
        keywords = self._extract_keywords(search_text)
        if not keywords:
            return None
        
        # 构建SQL查询
        conditions = []
        params = [book_id]
        
        for keyword in keywords:
            conditions.append("text_content LIKE ?")
            params.append(f'%{keyword}%')
        
        query = f'''
            SELECT * FROM book_positions 
            WHERE book_id = ? AND ({" OR ".join(conditions)})
            ORDER BY spine_index ASC
            LIMIT 1
        '''
        
        conn = sqlite3.connect(self.db_path)
        result = conn.execute(query, params).fetchone()
        conn.close()
        
        if result:
            return self._create_location_info(result, "keyword_match", 0.6)
        return None
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取，实际可以使用更复杂的NLP方法
        words = re.findall(r'\w+', text.lower())
        # 过滤常见词汇
        stopwords = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        keywords = [word for word in words if len(word) > 2 and word not in stopwords]
        return keywords[:5]  # 取前5个关键词
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        # 简单的Jaccard相似度
        set1 = set(text1.lower().split())
        set2 = set(text2.lower().split())
        
        intersection = len(set1 & set2)
        union = len(set1 | set2)
        
        return intersection / union if union > 0 else 0.0
    
    def _create_location_info(self, db_result, method: str, confidence: float) -> LocationInfo:
        """从数据库结果创建LocationInfo对象"""
        return LocationInfo(
            method=method,
            spine_index=db_result[3],
            chapter_href=db_result[2],
            search_text=db_result[4],
            element_hints={
                "tag": db_result[6] or "",
                "id": db_result[7] or "",
                "class": db_result[8] or ""
            },
            context_before=db_result[9] or "",
            context_after=db_result[10] or "",
            confidence=confidence
        )
    
    def get_recommendation_response(self, target_book_id: str, 
                                 target_text: str, 
                                 book_title: str = None) -> Dict:
        """生成推荐响应"""
        location = self.find_target_location(target_book_id, target_text)
        
        if not location:
            return {"error": "Target location not found"}
        
        return {
            "book_id": target_book_id,
            "book_title": book_title or f"Book_{target_book_id}",
            "location_info": {
                "method": location.method,
                "spine_index": location.spine_index,
                "chapter_href": location.chapter_href,
                "search_text": location.search_text,
                "element_hints": location.element_hints,
                "context_before": location.context_before,
                "context_after": location.context_after,
                "confidence": location.confidence
            },
            "preview_text": location.search_text[:200] + "..." if len(location.search_text) > 200 else location.search_text,
            "relation_type": "related_content"
        }

# 示例用法
if __name__ == "__main__":
    import numpy as np
    
    service = BookNavigationService()
    
    # 索引书籍
    book_id = service.index_book("path/to/your/book.epub")
    
    # 查找目标位置
    recommendation = service.get_recommendation_response(
        book_id, 
        "目标文本内容",
        "书籍标题"
    )
    
    print(json.dumps(recommendation, indent=2, ensure_ascii=False))
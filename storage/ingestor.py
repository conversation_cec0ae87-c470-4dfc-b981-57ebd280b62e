
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import ebooklib
from ebooklib import epub
from bs4 import BeautifulSoup
import os
import shutil
import uuid
import logging
import json
import hashlib
from typing import Dict, Any

# 假设 StorageManager 和 text_utils 在项目路径中可用
from storage.storage_manager import StorageManager
from processing import text_utils

# --- 设置日志 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', force=True)
logger = logging.getLogger(__name__)

# --- 配置 ---
# 媒体文件存储根目录，相对于项目根目录
MEDIA_ROOT = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'media')

class EpubIngestor:
    """
    EPUB文件内容摄取器。
    负责将EPUB文件解析并将其内容结构化地存入数据库，为后续的处理（分页、向量化等）做准备。
    """

    def __init__(self, epub_path: str, storage_manager: StorageManager):
        """
        初始化EPUB摄取器。

        Args:
            epub_path (str): 要处理的EPUB文件路径。
            storage_manager (StorageManager): 数据库存储管理器实例。
        """
        self.epub_path = epub_path
        self.storage = storage_manager
        self.book_id = None
        self.book_name = os.path.splitext(os.path.basename(epub_path))[0]
        # 全局内容块序号，用于确保所有内容块在全书范围内有唯一且递增的顺序
        self.global_block_sequence = 0

    def ingest(self):
        """
        执行EPUB文件的摄取主流程。
        包括：读取文件、提取元数据、存入书籍记录、处理图片、处理章节和内容块。
        """
        logger.info(f"开始摄取EPUB文件: {self.epub_path}")
        
        # 1. 使用 ebooklib 读取EPUB文件
        try:
            book = epub.read_epub(self.epub_path)
        except Exception as e:
            logger.error(f"读取EPUB文件失败 {self.epub_path}: {e}")
            return

        # 2. 提取书籍元数据并添加/更新书籍记录
        metadata = {
            'title': book.get_metadata('DC', 'title')[0][0] if book.get_metadata('DC', 'title') else self.book_name,
            'creator': book.get_metadata('DC', 'creator')[0][0] if book.get_metadata('DC', 'creator') else '未知',
            'language': book.get_metadata('DC', 'language')[0][0] if book.get_metadata('DC', 'language') else 'en',
            'identifier': book.get_metadata('DC', 'identifier')[0][0] if book.get_metadata('DC', 'identifier') else str(uuid.uuid4())
        }
        
        # 检查书籍是否已存在（基于源文件路径）
        existing_book = self.storage.get_book_by_source_path(self.epub_path)
        if existing_book:
            self.book_id = existing_book['id']
            logger.info(f"书籍 {self.book_name} 已存在 (ID: {self.book_id})。跳过摄取。")
            return

        # 计算EPUB文件的内容哈希值，用于唯一性校验
        sha256_hash = hashlib.sha256()
        with open(self.epub_path, "rb") as f:
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        content_hash = sha256_hash.hexdigest()

        # 从内容中检测语言（如果元数据中没有或为了更准确）
        # 理想情况下，这由 core_processor 或专用的语言检测器完成
        # 目前，我们简化处理，使用元数据中的语言
        detected_language = metadata['language']
        self.book_language = detected_language

        # 将书籍基本信息存入数据库
        self.book_id = self.storage.add_book(
            source_path=self.epub_path,
            content_hash=content_hash,
            detected_language=detected_language,
            metadata=metadata
        )
        logger.info(f"已添加书籍 {self.book_name}，ID: {self.book_id}")

        # 3. 处理并保存书籍中的图片
        self._ingest_images(book)

        # 4. 处理并保存章节及内容块（段落、标题等）
        self._ingest_content_blocks(book)

        # 5. 标记摄取完成（第一层处理完成）
        # 此时书籍已准备好进行核心处理（分页、向量化、摘要生成等）
        self.storage.update_book_status(self.book_id, 'ingested')
        logger.info(f"书籍 {self.book_id} 的摄取流程完成。状态: ingested.")

    def _ingest_images(self, book):
        """
        处理并保存EPUB中的图片文件。

        Args:
            book: 已由ebooklib解析的EPUB书籍对象。
        """
        logger.info("开始处理图片...")
        book_media_dir = os.path.join(MEDIA_ROOT, self.book_id)
        os.makedirs(book_media_dir, exist_ok=True)

        for item in book.get_items_of_type(ebooklib.ITEM_IMAGE):
            file_name = item.get_name()
            
            # 清理文件名，避免路径遍历问题并确保文件名有效
            # 使用 os.path.basename 仅获取文件名部分
            cleaned_file_name = os.path.basename(file_name)
            
            save_path = os.path.join(book_media_dir, cleaned_file_name)
            
            # 确保图片的父级目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)

            try:
                with open(save_path, 'wb') as f:
                    f.write(item.get_content())
                logger.debug(f"图片已保存: {save_path}")
            except Exception as e:
                logger.warning(f"无法保存图片 {file_name}: {e}")

    def _ingest_content_blocks(self, book):
        """
        处理并保存书籍中的章节和内容块。
        内容块是构成书籍内容的基本单位，如段落<p>、标题<h1>等。
        This method now filters out blocks that do not contain any text or images.

        Args:
            book: 已由ebooklib解析的EPUB书籍对象。
        """
        logger.info("开始处理内容块和句子...")
        logger.debug(f"书籍书脊 (Spine): {book.spine}")
        
        # ebooklib的spine给出了线性阅读顺序
        for chapter_idx, (item_id, linear_flag) in enumerate(book.spine):
            logger.debug(f"处理书脊项目: ID={item_id}, linear_flag={linear_flag}, 索引={chapter_idx}")
            item = book.get_item_with_id(item_id)
            if not item: 
                logger.warning(f"在书籍中未找到ID为 {item_id} 的项目。跳过。")
                continue

            # 检查项目的实际类型
            if item.get_type() == ebooklib.ITEM_DOCUMENT:

                chapter_href = item.get_name()
                # 使用文件名作为章节标题占位符，实际标题应从TOC获取
                chapter_title = item.get_name() 

                # 添加章节记录到数据库
                logger.debug(f"添加章节: book_id={self.book_id}, chapter_index={chapter_idx}, title={chapter_title}")
                chapter_id = self.storage.add_chapter(
                    book_id=self.book_id,
                    chapter_index=chapter_idx, 
                    title=chapter_title,
                    spine_index=chapter_idx, 
                    chapter_href=chapter_href
                )
                logger.debug(f"章节已添加，ID: {chapter_id}")
                
                # 获取章节的HTML内容并解析
                html_content = item.get_content() # 这是字节串
                soup = BeautifulSoup(html_content, 'html.parser')

                # 遍历body的直接子元素（块级元素）
                if not soup.body:
                    continue

                for element in soup.body.find_all(True, recursive=False):
                    element_text = element.get_text().strip()
                    is_image = element.find('img')

                    # Skip elements that have no text and no images
                    if not element_text and not is_image:
                        continue

                    self.global_block_sequence += 1
                    block_type = element.name
                    html_content_str = str(element)
                    block_metadata = None

                    # 检查此元素是否包含图片
                    if is_image:
                        original_src = is_image.get('src')
                        if original_src:
                            img_item_name = None
                            # 在ebooklib的图片项目中查找匹配的图片
                            for img_item in book.get_items_of_type(ebooklib.ITEM_IMAGE):
                                if os.path.basename(img_item.get_name()) == os.path.basename(original_src):
                                    img_item_name = img_item.get_name()
                                    break
                            
                            if img_item_name:
                                cleaned_img_name = os.path.basename(img_item_name)
                                # 构造图片的公共访问URL
                                public_url = f"/media/{self.book_id}/{cleaned_img_name}"
                                block_metadata = {'src': public_url, 'alt': is_image.get('alt', '')}
                                # 定义新的块类型，表示这是一个图片容器
                                block_type = 'img_container'
                            else:
                                logger.warning(f"在ebooklib中未找到src为: {original_src} 的图片项目")
                                # 回退到占位符图片
                                block_metadata = {'src': '/media/placeholder.png', 'alt': is_image.get('alt', '')}
                        else:
                            logger.warning(f"发现没有src属性的img标签: {is_image}")
                            # 回退到占位符图片
                            block_metadata = {'src': '/media/placeholder.png', 'alt': is_image.get('alt', '')}

                    # 将内容块信息存入数据库
                    block_id = self.storage.add_content_block(
                        book_id=self.book_id,
                        chapter_id=chapter_id,
                        sequence=self.global_block_sequence,
                        block_type=block_type,
                        html_content=html_content_str,
                        metadata=block_metadata
                    )

                    # 如果是包含文本的块，则进行句子切分并存入数据库
                    # 仅处理文本块，不处理纯图片容器
                    if block_type in ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'blockquote']:
                        # 使用工具函数按语言切分句子
                        sentences = text_utils.parse_text(element_text, self.book_language)
                        for i, sentence_text in enumerate(sentences):
                            # 将句子存入数据库，并关联到其所属的内容块
                            self.storage.add_sentence(
                                book_id=self.book_id,
                                chapter_id=chapter_id,
                                block_id=block_id,
                                sequence_in_block=i,
                                content=sentence_text
                            )

# --- 用于测试的主执行入口 ---
if __name__ == "__main__":
    project_root = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..')
    corpus_path = os.path.join(project_root, "corpus")
    
    # 确保媒体目录存在
    os.makedirs(MEDIA_ROOT, exist_ok=True)

    # 重建数据库以进行干净测试
    storage = StorageManager(rebuild_db=True)

    # 摄取《网络科学》
    epub_ns_path = os.path.join(corpus_path, "Network_Science.epub")
    ingestor_ns = EpubIngestor(epub_ns_path, storage)
    ingestor_ns.ingest()

    # 摄取《老人与海》
    epub_oldman_path = os.path.join(corpus_path, "老人与海.epub")
    ingestor_oldman = EpubIngestor(epub_oldman_path, storage)
    ingestor_oldman.ingest()

    logger.info("所有书籍的摄取流程已完成。")

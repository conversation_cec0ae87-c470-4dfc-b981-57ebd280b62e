# storage/remove_book.py
import sys
import os
import argparse

# 将项目根目录添加到路径中以便导入，假设此脚本从项目根目录运行
# 或者如果从其他地方运行，则调整路径计算。
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from storage.storage_manager import StorageManager

def remove_book_by_id(book_id: str) -> bool:
    r"""
    从数据库和向量库中删除指定ID的书籍及其所有相关数据。
    
    Args:
        book_id (str): 要删除的书籍ID。

    Returns:
        bool: 删除是否成功。
    """
    success = True
    print(f"尝试删除ID为 {book_id} 的书籍...")

    try:
        # StorageManager 初始化时会连接数据库
        storage = StorageManager()
        
        # 检查书籍是否存在
        book_record = storage.get_book_by_id(book_id)
        if not book_record:
            print(f"在数据库中未找到ID为 '{book_id}' 的书籍。")
            return False
            
        source_path = book_record['source_path']
        print(f"找到书籍: '{os.path.basename(source_path)}' (ID: {book_id})")

        # --- 2. 从 SQLite 数据库删除数据 ---
        # SQLite 的外键约束 (ON DELETE CASCADE) 会自动删除 chapters, sentences, pages, processed_data
        print("从SQLite数据库删除书籍记录...")
        storage.cursor.execute("DELETE FROM books WHERE id = ?", (book_id,))
        rows_affected = storage.cursor.rowcount
        storage.conn.commit() # 显式提交事务
        if rows_affected > 0:
            print(f"  - 成功从 'books' 表中删除书籍记录。")
        else:
            print(f"  - 警告: 未从 'books' 表中删除ID为 {book_id} 的行。")
            success = False

        # --- 3. 从 ChromaDB 向量库删除数据 ---
        print("从ChromaDB删除相关向量...")
        try:
            # 删除与该书籍相关的所有页面向量
            storage.page_collection.delete(where={"book_id": book_id})
            print("  - 成功删除页面向量。")
        except Exception as e:
            print(f"  - 警告: 删除页面向量失败: {e}")

        try:
            # 删除与该书籍相关的所有摘要向量
            storage.summary_collection.delete(where={"book_id": book_id})
            print("  - 成功删除摘要向量。")
        except Exception as e:
            print(f"  - 警告: 删除摘要向量失败: {e}")

        try:
            # 删除与该书籍相关的所有句子向量
            storage.sentence_collection.delete(where={"book_id": book_id})
            print("  - 成功删除句子向量。")
        except Exception as e:
            print(f"  - 警告: 删除句子向量失败: {e}")
            
        print(f"书籍删除过程完成，ID: {book_id}。")
        return success

    except Exception as e:
        print(f"An error occurred during the removal process: {e}")
        return False


def remove_book_by_source_path(source_path: str) -> bool:
    r"""
    根据源文件路径从数据库和向量库中删除书籍。
    为方便用户使用，提供一个通过文件路径查找并删除的接口。
    
    Args:
        source_path (str): 书籍源文件的完整路径。

    Returns:
        bool: 删除是否成功。
    """
    print(f"尝试删除源文件路径为 {source_path} 的书籍...")
    try:
        storage = StorageManager()
        book_record = storage.get_book_by_source_path(source_path)
        if not book_record:
            print(f"在数据库中未找到源文件路径为 '{source_path}' 的书籍。")
            return False
        book_id = book_record['id']
        print(f"找到书籍ID: {book_id}")
        return remove_book_by_id(book_id)
    except Exception as e:
        print(f"An error occurred while looking up the book by path: {e}")
        return False


def list_all_books():
    r"""列出数据库中所有书籍及其ID，方便用户选择。"""
    print("列出数据库中的所有书籍:")
    try:
        storage = StorageManager()
        storage.cursor.execute("SELECT id, source_path, detected_language, status FROM books")
        books = storage.cursor.fetchall()
        if not books:
            print("  数据库中未找到书籍。")
            return
        for book in books:
            filename = os.path.basename(book['source_path'])
            print(f"  ID: {book['id']}, 文件: {filename}, 语言: {book['detected_language']}, 状态: {book['status']}")
    except Exception as e:
        print(f"An error occurred while listing books: {e}")


def main():
    r"""主函数，处理命令行参数。"""
    parser = argparse.ArgumentParser(
        description="从Read Agent数据库和向量存储中删除书籍及其所有相关数据。",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument(
        '--id',
        type=str,
        help='要删除的书籍的唯一ID (例如, d210ecc9-fda2-4fdc-8fb4-2c1b4051983e).'
    )
    parser.add_argument(
        '--path',
        type=str,
        help='要删除的书籍的完整源文件路径 (例如, /path/to/corpus/老人与海.epub).'
    )
    parser.add_argument(
        '--list',
        action='store_true',
        help='列出数据库中的所有书籍及其ID。'
    )

    args = parser.parse_args()

    # 检查参数互斥性
    if args.list:
        if args.id or args.path:
            print("错误: --list 不能与 --id 或 --path 一起使用。")
            sys.exit(1)
        list_all_books()
        sys.exit(0)

    if args.id and args.path:
        print("错误: 请仅指定 --id 或 --path 之一。")
        sys.exit(1)
        
    if not args.id and not args.path:
        print("错误: 必须指定 --id 或 --path 之一。使用 --list 查看可用书籍。")
        sys.exit(1)

    success = False
    if args.id:
        success = remove_book_by_id(args.id)
    elif args.path:
        success = remove_book_by_source_path(args.path)
        
    if success:
        print("\n书籍删除成功。")
        sys.exit(0)
    else:
        print("\n书籍删除失败或遇到警告。请检查上面的输出。")
        sys.exit(1)

if __name__ == "__main__":
    main()
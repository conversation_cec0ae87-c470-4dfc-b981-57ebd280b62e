# dev/storage/storage_manager.py
import hashlib
from config import (
    DATA_DIR, SQLITE_DB_NAME,
    CHROMA_COLLECTION_NAME, CHROMA_SUMMARY_COLLECTION_NAME, CHROMA_SENTENCE_COLLECTION_NAME
)
from chromadb.api.models.Collection import Collection
import chromadb
import sqlite3
import json
import os
import uuid
import logging
from typing import List, Dict, Optional, Any, Tuple
import shutil
import sys


# 将项目根目录添加到路径中以便导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class StorageManager:
    def __init__(self, rebuild_db: bool = False):
        os.makedirs(DATA_DIR, exist_ok=True)
        db_path = os.path.join(DATA_DIR, SQLITE_DB_NAME)

        if rebuild_db and os.path.exists(db_path):
            logger.warning(
                "重新构建数据库：所有现有数据将被删除。" # This is a string literal, no escaping needed here.
            )
            chroma_path = os.path.join(DATA_DIR, "chroma_db")
            if os.path.exists(chroma_path):
                shutil.rmtree(chroma_path)
            os.remove(db_path)

        self.conn = sqlite3.connect(db_path)
        self.conn.row_factory = self._dict_factory
        self.cursor = self.conn.cursor()

        self.setup_databases()

        chroma_path = os.path.join(DATA_DIR, "chroma_db")
        self.chroma_client = chromadb.PersistentClient(path=chroma_path)
        self.page_collection: Collection = self.chroma_client.get_or_create_collection(
            name=CHROMA_COLLECTION_NAME)
        self.summary_collection: Collection = self.chroma_client.get_or_create_collection(
            name=CHROMA_SUMMARY_COLLECTION_NAME)
        self.sentence_collection: Collection = self.chroma_client.get_or_create_collection(
            name=CHROMA_SENTENCE_COLLECTION_NAME)

    @staticmethod
    def _dict_factory(cursor, row) -> Dict[str, Any]:
        d = {}
        for idx, col in enumerate(cursor.description):
            d[col[0]] = row[idx]
        return d

    def setup_databases(self):
        """
        初始化所有数据库表。
        表结构设计遵循“内容即服务”架构，将内容分解为书籍、章节、内容块、句子和逻辑页面等不同粒度。
        """
        # 创建书籍表：存储书籍的基本元数据和处理状态
        self.cursor.execute("""
        CREATE TABLE IF NOT EXISTS books (
            id TEXT PRIMARY KEY,
            source_path TEXT NOT NULL UNIQUE,
            content_hash TEXT NOT NULL,
            detected_language TEXT,
            metadata TEXT,
            status TEXT NOT NULL DEFAULT 'new',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        )

        # 创建章节表：存储书籍内章节的信息，与书籍表关联
        self.cursor.execute("""
        CREATE TABLE IF NOT EXISTS chapters (
            id TEXT PRIMARY KEY,
            book_id TEXT NOT NULL,
            chapter_index INTEGER NOT NULL,
            title TEXT,
            spine_index INTEGER,
            chapter_href TEXT,
            FOREIGN KEY (book_id) REFERENCES books (id) ON DELETE CASCADE
        )
        """
        )

        # 创建内容块表：存储书籍中的内容块（如段落<p>、标题<h1>等）
        # sequence: 决定了内容块在全书中的绝对顺序
        # block_type: 内容块的类型，如 'h1', 'p', 'img', 'li', 'blockquote'
        # html_content: 存储原始HTML，用于回溯和调试
        # metadata: 存储与块相关的额外信息，如图片的src、alt等
        self.cursor.execute("""
        CREATE TABLE IF NOT EXISTS content_blocks (
            id TEXT PRIMARY KEY,
            book_id TEXT NOT NULL,
            chapter_id TEXT NOT NULL,
            sequence INTEGER NOT NULL,
            block_type TEXT NOT NULL,
            html_content TEXT,
            metadata TEXT,
            FOREIGN KEY (book_id) REFERENCES books (id) ON DELETE CASCADE,
            FOREIGN KEY (chapter_id) REFERENCES chapters (id) ON DELETE CASCADE
        )
        """
        )

        # 创建句子表：存储从内容块中切分出的句子
        # block_id: 关键外键，关联到所属的 content_blocks 记录
        # sequence_in_block: 句子在其所属内容块内的顺序
        # content: 句子的纯文本内容
        # text_hash: 句子内容的哈希值，用于快速去重
        self.cursor.execute("""
        CREATE TABLE IF NOT EXISTS sentences (
            id TEXT PRIMARY KEY,
            book_id TEXT NOT NULL,
            chapter_id TEXT NOT NULL,
            block_id TEXT NOT NULL,
            sequence_in_block INTEGER NOT NULL,
            content TEXT NOT NULL,
            text_hash TEXT,
            FOREIGN KEY (block_id) REFERENCES content_blocks (id) ON DELETE CASCADE
        )
        """
        )

        # 创建逻辑页面表：定义由句子范围构成的逻辑页面
        # page_index: 页面在书中的索引
        # start_sentence_id: 页面起始句子的ID
        # end_sentence_id: 页面结束句子的ID
        # summary: 页面的AI生成摘要（可选）
        self.cursor.execute("""
        CREATE TABLE IF NOT EXISTS pages (
            id TEXT PRIMARY KEY,
            book_id TEXT NOT NULL,
            page_index INTEGER NOT NULL,
            start_sentence_id TEXT NOT NULL,
            end_sentence_id TEXT NOT NULL,
            FOREIGN KEY (book_id) REFERENCES books (id) ON DELETE CASCADE
        )
        """
        )

        # 创建处理数据表：存储由插件生成的处理结果（如页面摘要）
        # page_id: 关联到所属的页面
        # processor_name: 处理器名称（如 'gisting'）
        # data: 处理结果的文本数据
        self.cursor.execute("""
        CREATE TABLE IF NOT EXISTS processed_data (
            id TEXT PRIMARY KEY,
            page_id TEXT NOT NULL,
            processor_name TEXT NOT NULL,
            data TEXT,
            FOREIGN KEY (page_id) REFERENCES pages (id) ON DELETE CASCADE
        )
        """
        )

        self.conn.commit()

    def add_book(self, source_path: str, content_hash: str, detected_language: str, metadata: dict) -> str:
        """添加一本书到数据库。"""
        book_uuid = str(uuid.uuid4())
        self.cursor.execute(
            "INSERT INTO books (id, source_path, content_hash, detected_language, metadata, status) VALUES (?, ?, ?, ?, ?, ?)",
            (book_uuid, source_path, content_hash, detected_language,
             json.dumps(metadata, ensure_ascii=False), 'parsed')
        )
        self.conn.commit()
        return book_uuid

    def add_chapter(self, book_id: str, chapter_index: int, title: str, spine_index: int, chapter_href: str) -> str:
        """添加一个章节到数据库。"""
        chapter_uuid = str(uuid.uuid4())
        self.cursor.execute(
            "INSERT INTO chapters (id, book_id, chapter_index, title, spine_index, chapter_href) VALUES (?, ?, ?, ?, ?, ?)",
            (chapter_uuid, book_id, chapter_index,
             title, spine_index, chapter_href)
        )
        self.conn.commit()
        return chapter_uuid

    def add_content_block(self, book_id: str, chapter_id: str, sequence: int, block_type: str, html_content: str, metadata: dict = None) -> str:
        """为书籍添加一个内容块。"""
        block_uuid = str(uuid.uuid4())
        self.cursor.execute(
            "INSERT INTO content_blocks (id, book_id, chapter_id, sequence, block_type, html_content, metadata) VALUES (?, ?, ?, ?, ?, ?, ?)",
            (block_uuid, book_id, chapter_id, sequence, block_type, html_content, json.dumps(metadata, ensure_ascii=False) if metadata else None)
        )
        self.conn.commit()
        return block_uuid

    def add_sentence(self, book_id: str, chapter_id: str, block_id: str, sequence_in_block: int, content: str) -> str:
        """添加一个句子到数据库，并关联到内容块。"""
        sentence_uuid = str(uuid.uuid4())
        text_hash = hashlib.sha256(content.encode('utf-8')).hexdigest()
        self.cursor.execute(
            "INSERT INTO sentences (id, book_id, chapter_id, block_id, sequence_in_block, content, text_hash) VALUES (?, ?, ?, ?, ?, ?, ?)",
            (sentence_uuid, book_id, chapter_id, block_id, sequence_in_block, content, text_hash)
        )
        self.conn.commit()
        return sentence_uuid

    def add_page(self, book_id: str, page_index: int, start_sentence_id: str, end_sentence_id: str) -> str:
        """添加一个逻辑页面到数据库。"""
        page_uuid = str(uuid.uuid4())
        self.cursor.execute(
            "INSERT INTO pages (id, book_id, page_index, start_sentence_id, end_sentence_id) VALUES (?, ?, ?, ?, ?)",
            (page_uuid, book_id, page_index, start_sentence_id, end_sentence_id)
        )
        self.conn.commit()
        return page_uuid

    def add_processed_data(self, page_id: str, processor_name: str, data: str) -> str:
        """添加处理后的数据到数据库。"""
        processed_data_uuid = str(uuid.uuid4())
        self.cursor.execute(
            "INSERT INTO processed_data (id, page_id, processor_name, data) VALUES (?, ?, ?, ?)",
            (processed_data_uuid, page_id, processor_name, data)
        )
        self.conn.commit()
        return processed_data_uuid

    def add_page_vector(self, page_id: str, vector: List[float], metadata: dict) -> bool:
        """为页面添加向量到ChromaDB。"""
        try:
            self.page_collection.add(ids=[page_id], embeddings=[
                                     vector], metadatas=[metadata])
            return True
        except Exception as e:
            logger.error(
                f"为页面 {page_id} 添加向量到ChromaDB时出错: {e}")
            return False

    def add_summary_vector(self, processed_data_id: str, vector: List[float], metadata: dict) -> bool:
        """为摘要添加向量到ChromaDB。"""
        try:
            self.summary_collection.add(ids=[processed_data_id], embeddings=[
                                        vector], metadatas=[metadata])
            return True
        except Exception as e:
            logger.error(
                f"为摘要 {processed_data_id} 添加向量到ChromaDB时出错: {e}")
            return False

    def add_sentence_vector(self, sentence_id: str, vector: List[float], metadata: dict) -> bool:
        """为句子添加向量到ChromaDB。"""
        try:
            self.sentence_collection.add(ids=[sentence_id], embeddings=[
                                         vector], metadatas=[metadata])
            return True
        except Exception as e:
            logger.error(
                f"为句子 {sentence_id} 添加向量到ChromaDB时出错: {e}")
            return False

    # --- Query and Retrieval Methods ---

    def get_all_book_source_paths(self) -> List[str]:
        """获取数据库中所有书籍的源文件路径列表。"""
        self.cursor.execute("SELECT source_path FROM books")
        return [row['source_path'] for row in self.cursor.fetchall()]

    def get_book_by_source_path(self, source_path: str) -> Optional[Dict[str, Any]]:
        """根据源文件路径查找书籍。"""
        self.cursor.execute(
            "SELECT * FROM books WHERE source_path = ?", (source_path,))
        return self.cursor.fetchone()

    def get_book_by_id(self, book_id: str) -> Optional[Dict[str, Any]]:
        """根据ID查找书籍。"""
        self.cursor.execute("SELECT * FROM books WHERE id = ?", (book_id,))
        return self.cursor.fetchone()

    def get_all_books(self) -> List[Dict[str, Any]]:
        """获取数据库中所有书籍的列表。"""
        self.cursor.execute("SELECT id, source_path, detected_language, metadata, status FROM books")
        books = self.cursor.fetchall()
        for book in books:
            if book.get('metadata'):
                try:
                    book['metadata'] = json.loads(book['metadata'])
                except json.JSONDecodeError:
                    logger.warning(f"Could not decode metadata for book {book['id']}")
                    book['metadata'] = {}
        return books

    def update_book_status(self, book_id: str, status: str):
        """更新书籍状态。"""
        self.cursor.execute(
            "UPDATE books SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
            (status, book_id)
        )
        self.conn.commit()
        logger.info(f"更新书籍 {book_id} 的状态为 '{status}'")

    def get_chapters_by_book_id(self, book_id: str) -> List[Dict[str, Any]]:
        """获取指定书籍的所有章节记录。"""
        self.cursor.execute(
            "SELECT * FROM chapters WHERE book_id = ? ORDER BY chapter_index ASC", (book_id,))
        return self.cursor.fetchall()

    def get_content_blocks_for_book(self, book_id: str) -> List[Dict[str, Any]]:
        """获取指定书籍的所有内容块，按顺序排列。"""
        self.cursor.execute(
            "SELECT * FROM content_blocks WHERE book_id = ? ORDER BY sequence ASC",
            (book_id,)
        )
        return self.cursor.fetchall()

    def get_sentences_for_block(self, block_id: str) -> List[Dict[str, Any]]:
        """获取指定内容块的所有句子，按顺序排列。"""
        self.cursor.execute(
            "SELECT * FROM sentences WHERE block_id = ? ORDER BY sequence_in_block ASC",
            (block_id,)
        )
        return self.cursor.fetchall()

    def get_sentences_for_book(self, book_id: str) -> List[Dict[str, Any]]:
        """获取指定书籍的所有句子记录，按正确顺序排列。"""
        self.cursor.execute(
            """
            SELECT s.*
            FROM sentences s
            JOIN content_blocks cb ON s.block_id = cb.id
            WHERE s.book_id = ?
            ORDER BY cb.sequence ASC, s.sequence_in_block ASC
            """,
            (book_id,)
        )
        return self.cursor.fetchall()

    def get_sentence_hashes_for_chapter(self, chapter_id: str) -> set:
        """获取指定章节的所有句子text_hashes集合。"""
        self.cursor.execute(
            "SELECT text_hash FROM sentences WHERE chapter_id = ?", (chapter_id,))
        return {row['text_hash'] for row in self.cursor.fetchall()}

    def get_all_pages_for_book(self, book_id: str) -> List[Dict[str, Any]]:
        """获取指定书籍的所有页面记录。"""
        self.cursor.execute(
            "SELECT * FROM pages WHERE book_id = ? ORDER BY page_index ASC",
            (book_id,)
        )
        return self.cursor.fetchall()

    def get_processed_page_ids(self, book_id: str, processor_name: str) -> set:
        """获取指定书籍中已由特定处理器处理的页面ID集合。"""
        self.cursor.execute(
            """
            SELECT pd.page_id
            FROM processed_data pd
            JOIN pages p ON pd.page_id = p.id
            WHERE p.book_id = ? AND pd.processor_name = ?
            """,
            (book_id, processor_name)
        )
        return {row['page_id'] for row in self.cursor.fetchall()}

    def get_vectorized_page_ids(self, book_id: str) -> set:
        """
        使用元数据过滤器获取指定书籍中已存在于ChromaDB中的页面ID集合。
        """
        try:
            found_vectors = self.page_collection.get(
                where={"book_id": book_id}, include=[])
            return set(found_vectors['ids'])
        except Exception as e:
            logger.error(
                f"从ChromaDB获取书籍 {book_id} 的向量化页面ID时出错: {e}")
            return set()

    def get_vectorized_sentence_ids(self, book_id: str) -> set:
        """
        使用元数据过滤器获取指定书籍中已存在于ChromaDB中的句子ID集合。
        """
        try:
            found_vectors = self.sentence_collection.get(
                where={"book_id": book_id}, include=[])
            return set(found_vectors['ids'])
        except Exception as e:
            logger.error(
                f"从ChromaDB获取书籍 {book_id} 的向量化句子ID时出错: {e}")
            return set()

    def _get_sentence_global_sort_key(self, sentence_id: str) -> Optional[Tuple[int, int]]:
        """获取句子的全局排序键 (block_sequence, sequence_in_block)。"""
        self.cursor.execute(
            """
            SELECT cb.sequence AS block_sequence, s.sequence_in_block
            FROM sentences s
            JOIN content_blocks cb ON s.block_id = cb.id
            WHERE s.id = ?
            """,
            (sentence_id,)
        )
        result = self.cursor.fetchone()
        if result:
            return (result['block_sequence'], result['sequence_in_block'])
        return None

    def get_sentence_by_id(self, sentence_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取句子。"""
        self.cursor.execute(
            "SELECT * FROM sentences WHERE id = ?", (sentence_id,))
        return self.cursor.fetchone()

    def get_chapter_by_id(self, chapter_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取章节。"""
        self.cursor.execute(
            "SELECT * FROM chapters WHERE id = ?", (chapter_id,))
        return self.cursor.fetchone()

    def get_processed_data_by_id(self, processed_data_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取处理后的数据。"""
        self.cursor.execute(
            "SELECT * FROM processed_data WHERE id = ?", (processed_data_id,))
        return self.cursor.fetchone()

    def get_page_by_sentence_id(self, sentence_id: str) -> Optional[Dict[str, Any]]:
        """根据句子ID获取其所属的逻辑页面。"""
        target_sort_key = self._get_sentence_global_sort_key(sentence_id)
        if target_sort_key is None:
            return None

        # Find the page whose start_sentence and end_sentence bracket the target sentence
        self.cursor.execute(
            """
            SELECT p.*
            FROM pages p
            JOIN sentences s_target ON s_target.id = ?
            JOIN sentences s_start ON p.start_sentence_id = s_start.id
            JOIN content_blocks cb_start ON s_start.block_id = cb_start.id
            JOIN sentences s_end ON p.end_sentence_id = s_end.id
            JOIN content_blocks cb_end ON s_end.block_id = cb_end.id
            WHERE p.book_id = s_target.book_id -- Ensure same book
              AND (cb_start.sequence < ? OR (cb_start.sequence = ? AND s_start.sequence_in_block <= ?))
              AND (cb_end.sequence > ? OR (cb_end.sequence = ? AND s_end.sequence_in_block >= ?))
            LIMIT 1
            """,
            (sentence_id, target_sort_key[0], target_sort_key[0], target_sort_key[1],
             target_sort_key[0], target_sort_key[0], target_sort_key[1])
        )
        return self.cursor.fetchone()

    def get_pages_by_ids(self, page_ids: List[str]) -> List[Dict[str, Any]]:
        """根据ID列表获取页面。"""
        if not page_ids:
            return []
        placeholders = ', '.join('?' for _ in page_ids)
        query = f"SELECT * FROM pages WHERE id IN ({placeholders})"
        self.cursor.execute(query, page_ids)
        return self.cursor.fetchall()

    def get_sentences_for_page(self, page_id: str) -> List[Dict[str, Any]]:
        """获取指定页面的所有句子记录。"""
        page_info = self.get_pages_by_ids([page_id])
        if not page_info:
            return []
        page_info = page_info[0]

        start_sentence_id = page_info['start_sentence_id']
        end_sentence_id = page_info['end_sentence_id']

        # Get sort keys for start and end sentences
        start_sort_key = self._get_sentence_global_sort_key(start_sentence_id)
        end_sort_key = self._get_sentence_global_sort_key(end_sentence_id)

        if start_sort_key is None or end_sort_key is None:
            logger.error(f"无法获取页面 {page_id} 的起始或结束句子的排序键。")
            return []

        # Query all sentences within the global sort key range
        self.cursor.execute(
            """
            SELECT s.*
            FROM sentences s
            JOIN content_blocks cb ON s.block_id = cb.id
            WHERE s.book_id = ?
              AND (cb.sequence > ? OR (cb.sequence = ? AND s.sequence_in_block >= ?))
              AND (cb.sequence < ? OR (cb.sequence = ? AND s.sequence_in_block <= ?))
            ORDER BY cb.sequence ASC, s.sequence_in_block ASC
            """,
            (page_info['book_id'],
             start_sort_key[0], start_sort_key[0], start_sort_key[1],
             end_sort_key[0], end_sort_key[0], end_sort_key[1])
        )
        return self.cursor.fetchall()

    def find_pages_by_text(self, book_id: str, query_text: str, limit: int = 10) -> List[Dict[str, Any]]:
        """根据文本在句子中查找，并返回这些句子所属的页面。"""
        # Note: This is a simplified search. A real implementation would use FTS5 for efficiency.
        self.cursor.execute(
            """SELECT p.* FROM pages p 
               JOIN sentences s_start ON p.start_sentence_id = s_start.id
               JOIN sentences s_end ON p.end_sentence_id = s_end.id
               JOIN sentences s_match ON s_match.book_id = p.book_id
               WHERE p.book_id = ? AND s_match.content LIKE ?
               AND s_match.id >= p.start_sentence_id AND s_match.id <= p.end_sentence_id
               GROUP BY p.id
               LIMIT ?
            """, (book_id, f'%{query_text}%', limit)
        )
        return self.cursor.fetchall()

    def _query_collection(self, collection: Collection, query_vector: List[float], book_id: Optional[str] = None, n_results: int = 10) -> Optional[Dict[str, list]]:
        """查询ChromaDB集合的辅助函数。"""
        try:
            query_args = {
                'query_embeddings': [query_vector],
                'n_results': n_results,
                'include': ['distances']
            }
            if book_id:
                query_args['where'] = {"book_id": book_id}

            return collection.query(**query_args)
        except Exception as e:
            logger.error(f"查询集合 {collection.name} 时出错: {e}")
            return None

    def query_page_vectors(self, query_vector: List[float], book_id: Optional[str] = None, n_results: int = 10) -> Optional[Dict[str, list]]:
        """查询页面向量。"""
        return self._query_collection(
            self.page_collection, query_vector, book_id, n_results
        )

    def query_summary_vectors(self, query_vector: List[float], book_id: Optional[str] = None, n_results: int = 10) -> Optional[Dict[str, list]]:
        """查询摘要向量。"""
        return self._query_collection(
            self.summary_collection, query_vector, book_id, n_results
        )

    def query_sentence_vectors(self, query_vector: List[float], book_id: Optional[str] = None, n_results: int = 10) -> Optional[Dict[str, list]]:
        """查询句子向量。"""
        return self._query_collection(
            self.sentence_collection, query_vector, book_id, n_results
        )

    def search_and_retrieve_pages(self, query_vector: List[float], book_id: Optional[str] = None, n_results: int = 10) -> List[Dict[str, Any]]:
        """
        执行向量搜索以查找相关的页面ID并检索完整的页面数据。

        Args:
            query_vector: 用户查询的向量表示。
            book_id: 可选的书籍ID以限制搜索范围。
            n_results: 要返回的结果数量。

        Returns:
            页面字典列表，每个字典包含完整的页面数据，按相关性排序。
        """
        logger.info(
            f"搜索书籍 {book_id} 的 {n_results} 个相关页面")

        # 1. 从ChromaDB获取相关的页面ID
        page_ids = self.query_page_vectors(
            query_vector=query_vector,
            book_id=book_id,
            n_results=n_results
        )

        if not page_ids:
            logger.warning("向量搜索未返回页面ID。")
            return []

        logger.info(f"找到 {len(page_ids)} 个相关页面ID: {page_ids}")

        # 2. 使用ID从SQLite获取页面内容
        pages = self.get_pages_by_ids(page_ids)

        if not pages:
            logger.warning(
                f"无法检索ID为 {page_ids} 的页面内容")
            return []

        # ChromaDB的结果按相关性排序。我们应该重新排序SQLite结果
        # 以匹配该顺序。
        page_id_to_page_map = {page['id']: page for page in pages}
        ordered_pages = [page_id_to_page_map[pid]
                         for pid in page_ids if pid in page_id_to_page_map]

        logger.info(f"成功检索到 {len(ordered_pages)} 个页面。")
        return ordered_pages

    def close(self):
        """关闭数据库连接。"""
        if self.conn:
            self.conn.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
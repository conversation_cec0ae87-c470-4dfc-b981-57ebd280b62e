# dev/system_plugins/default_processors.py
import pluggy
import logging
from processing.pipeline_objects import Page
from llm.llm_interface import query_llm, clean_llm_response
from llm.prompt_manager import PromptManager
from config import LANGUAGE_MAPPING

hookimpl = pluggy.HookimplMarker("read_agent")
logger = logging.getLogger(__name__)

@hookimpl
def process_page(page: Page, detected_language: str):
    """
    一个页面处理器插件，为页面生成摘要 (gisting)。
    此函数经过增强，包含了详细的错误处理和日志记录。
    """
    # 为了解耦，插件在需要时自行初始化一个PromptManager。
    # 在一个更大型的应用中，这可以通过依赖注入来优化。
    prompt_manager = PromptManager()
    
    gisting_template = prompt_manager.get_template('gisting')
    if not gisting_template:
        logger.warning("未找到 'gisting' 提示模板，跳过页面摘要生成。")
        return

    prompt = gisting_template.format(
        language=LANGUAGE_MAPPING.get(detected_language, 'English'),
        page_text=page.content
    )
    
    try:
        raw_summary = query_llm(prompt)
        if not raw_summary:
            logger.warning(f"页面 {page.page_id} 的摘要生成返回了空内容。")
            return

        summary = clean_llm_response(raw_summary)
        
        # 将处理结果存回传入的Page对象中
        if summary:
            page.processed_data['gisting'] = summary
        else:
            logger.warning(f"页面 {page.page_id} 的摘要在清理后变为空。原始返回: '{raw_summary[:100]}...'")

    except Exception as e:
        logger.error(f"为页面 {page.page_id} 生成摘要时发生错误: {e}", exc_info=True)